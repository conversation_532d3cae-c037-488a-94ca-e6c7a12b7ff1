{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "globals": {"Neutralino": "readonly"}, "plugins": ["react"], "rules": {"indent": ["warn", "tab"], "linebreak-style": ["error", "unix"], "quotes": ["error", "single"], "semi": ["warn", "always"], "no-trailing-spaces": ["warn"], "no-use-before-define": "error", "no-unused-vars": ["warn"], "no-debugger": ["warn"], "no-console": ["warn"]}}