{"applicationId": "js.neutralino.sample", "version": "1.0.0", "defaultMode": "window", "documentRoot": "/dist/", "url": "/", "port": 3000, "enableServer": true, "enableNativeAPI": true, "tokenSecurity": "none", "logging": {"enabled": true, "writeToLogFile": true}, "nativeAllowList": ["filesystem.*", "window.*", "app.*", "events.*", "os.showNotification", "computer.*"], "globalVariables": {"TEST1": "Hello"}, "modes": {"window": {"title": "Gaming Cafe Client", "width": 800, "height": 500, "minWidth": 400, "minHeight": 200, "fullScreen": true, "alwaysOnTop": true, "icon": "/public/neutralino.png", "enableInspector": false, "borderless": true, "maximize": true, "hidden": false, "resizable": false, "exitProcessOnClose": true}}, "cli": {"binaryName": "Xpanel", "resourcesPath": "/dist/", "extensionsPath": "/extensions/", "binaryVersion": "5.1.0", "clientVersion": "5.1.0", "frontendLibrary": {"patchFile": "./index.html", "projectPath": "./", "devUrl": "http://localhost:5174", "devCommand": "yarn vite-dev", "buildCommand": "yarn vite-build"}}}