/**
 * Windows System Commands for Device Control
 * Executes system commands like poweroff, reboot, lock, and sleep
 */

import { os } from '@neutralinojs/lib';

/**
 * Execute Windows PowerShell command
 * @param {string} command - PowerShell command to execute
 * @param {string} description - Description for logging
 * @returns {Promise<Object>} Result object with success status and output
 */
async function executeWindowsCommand(command, description) {
  try {
    console.log(`🔧 Executing ${description}:`, command);
    
    const result = await os.execCommand(`powershell.exe -Command "${command}"`);
    
    console.log(`✅ ${description} executed successfully:`, result);
    
    return {
      success: true,
      output: result.stdOut || result.stdErr || 'Command executed',
      command: command,
      description: description
    };
  } catch (error) {
    console.error(`❌ Error executing ${description}:`, error);
    
    return {
      success: false,
      error: error.message || 'Unknown error',
      command: command,
      description: description
    };
  }
}

/**
 * Power off the Windows system
 * @param {number} delay - Delay in seconds (default: 5)
 * @returns {Promise<Object>} Result object
 */
export async function powerOffSystem(delay = 5) {
  const command = `shutdown /s /t ${delay} /c "System shutdown initiated by Gaming Cafe Client"`;
  return await executeWindowsCommand(command, 'System Power Off');
}

/**
 * Reboot the Windows system
 * @param {number} delay - Delay in seconds (default: 5)
 * @returns {Promise<Object>} Result object
 */
export async function rebootSystem(delay = 5) {
  const command = `shutdown /r /t ${delay} /c "System reboot initiated by Gaming Cafe Client"`;
  return await executeWindowsCommand(command, 'System Reboot');
}

/**
 * Lock the Windows screen
 * @returns {Promise<Object>} Result object
 */
export async function lockScreen() {
  const command = `rundll32.exe user32.dll,LockWorkStation`;
  return await executeWindowsCommand(command, 'Screen Lock');
}

/**
 * Put Windows system to sleep
 * @returns {Promise<Object>} Result object
 */
export async function sleepSystem() {
  // Use powercfg to hibernate or sleep
  const command = `rundll32.exe powrprof.dll,SetSuspendState 0,1,0`;
  return await executeWindowsCommand(command, 'System Sleep');
}

/**
 * Cancel any pending shutdown/reboot
 * @returns {Promise<Object>} Result object
 */
export async function cancelShutdown() {
  const command = `shutdown /a`;
  return await executeWindowsCommand(command, 'Cancel Shutdown');
}

/**
 * Get system power status
 * @returns {Promise<Object>} Result object with power status
 */
export async function getSystemPowerStatus() {
  const command = `Get-WmiObject -Class Win32_Battery | Select-Object EstimatedChargeRemaining, BatteryStatus`;
  return await executeWindowsCommand(command, 'Get Power Status');
}

/**
 * Show system notification before executing command
 * @param {string} action - Action being performed
 * @param {number} delay - Delay before execution
 */
export async function showSystemNotification(action, delay = 5) {
  try {
    if (window.Neutralino && window.Neutralino.os && window.Neutralino.os.showNotification) {
      await window.Neutralino.os.showNotification({
        summary: 'Gaming Cafe System Control',
        body: `${action} will be executed in ${delay} seconds. Please save your work.`
      });
    }
  } catch (error) {
    console.log('Could not show notification:', error);
  }
}

/**
 * Execute device control command based on type
 * @param {string} commandType - Type of command (powerOff, reboot, lock, sleep)
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Result object
 */
export async function executeDeviceCommand(commandType, options = {}) {
  const { delay = 5, showNotification = true } = options;
  
  console.log(`🎮 Device command requested: ${commandType}`);
  
  try {
    // Show notification before executing (except for lock which is immediate)
    if (showNotification && commandType !== 'lock') {
      await showSystemNotification(getCommandDescription(commandType), delay);
    }
    
    let result;
    
    switch (commandType.toLowerCase()) {
      case 'poweroff':
      case 'shutdown':
        result = await powerOffSystem(delay);
        break;
        
      case 'reboot':
      case 'restart':
        result = await rebootSystem(delay);
        break;
        
      case 'lock':
        result = await lockScreen();
        break;
        
      case 'sleep':
        result = await sleepSystem();
        break;
        
      default:
        throw new Error(`Unknown command type: ${commandType}`);
    }
    
    console.log(`✅ Device command ${commandType} executed:`, result);
    return result;
    
  } catch (error) {
    console.error(`❌ Error executing device command ${commandType}:`, error);
    return {
      success: false,
      error: error.message,
      commandType: commandType
    };
  }
}

/**
 * Get human-readable description for command type
 * @param {string} commandType - Command type
 * @returns {string} Description
 */
function getCommandDescription(commandType) {
  switch (commandType.toLowerCase()) {
    case 'poweroff':
    case 'shutdown':
      return 'System Shutdown';
    case 'reboot':
    case 'restart':
      return 'System Restart';
    case 'lock':
      return 'Screen Lock';
    case 'sleep':
      return 'System Sleep';
    default:
      return 'System Command';
  }
}

/**
 * Validate if command can be executed
 * @param {string} commandType - Command type to validate
 * @returns {boolean} True if command is valid
 */
export function isValidCommand(commandType) {
  const validCommands = ['poweroff', 'shutdown', 'reboot', 'restart', 'lock', 'sleep'];
  return validCommands.includes(commandType.toLowerCase());
}

/**
 * Get all available commands
 * @returns {Array} Array of available command types
 */
export function getAvailableCommands() {
  return [
    { type: 'poweroff', description: 'Power Off System', icon: '⚡', dangerous: true },
    { type: 'reboot', description: 'Restart System', icon: '🔄', dangerous: true },
    { type: 'lock', description: 'Lock Screen', icon: '🔒', dangerous: false },
    { type: 'sleep', description: 'Sleep System', icon: '😴', dangerous: false }
  ];
}
