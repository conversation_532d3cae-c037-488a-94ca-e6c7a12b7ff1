/**
 * Helper functions for the gaming cafe client app
 */

/**
 * Get saved login information from localStorage
 * @returns {Object|null} Saved login info or null if not found
 */
export function getSavedLoginInfo() {
  try {
    const savedInfo = localStorage.getItem('user_login_info');
    if (savedInfo) {
      const parsedInfo = JSON.parse(savedInfo);
      console.log('📋 Retrieved saved login info:', parsedInfo);
      return parsedInfo;
    }
    console.log('📋 No saved login info found');
    return null;
  } catch (error) {
    console.error('❌ Error parsing saved login info:', error);
    // Clear corrupted data
    localStorage.removeItem('user_login_info');
    return null;
  }
}

/**
 * Save login information to localStorage
 * @param {Object} loginInfo - Login information to save
 */
export function saveLoginInfo(loginInfo) {
  try {
    const infoToSave = {
      ...loginInfo,
      savedAt: new Date().toISOString()
    };
    localStorage.setItem('user_login_info', JSON.stringify(infoToSave));
    console.log('💾 Login info saved successfully:', infoToSave);
  } catch (error) {
    console.error('❌ Error saving login info:', error);
  }
}

/**
 * Clear saved login information
 */
export function clearSavedLoginInfo() {
  try {
    localStorage.removeItem('user_login_info');
    localStorage.removeItem('client_app_login');
    console.log('🗑️ Saved login info cleared');
  } catch (error) {
    console.error('❌ Error clearing saved login info:', error);
  }
}

/**
 * Get saved device information from localStorage
 * @returns {Object|null} Saved device info or null if not found
 */
export function getSavedDeviceInfo() {
  try {
    // Check persistent storage first
    let savedInfo = localStorage.getItem('persistent_device_info');
    let isPersistent = true;

    if (!savedInfo) {
      savedInfo = localStorage.getItem('device_info');
      isPersistent = false;
    }

    if (savedInfo) {
      const parsedInfo = JSON.parse(savedInfo);
      console.log('📱 Retrieved saved device info:', parsedInfo, 'Persistent:', isPersistent);
      return parsedInfo;
    }
    console.log('📱 No saved device info found');
    return null;
  } catch (error) {
    console.error('❌ Error parsing saved device info:', error);
    // Clear corrupted data
    localStorage.removeItem('device_info');
    localStorage.removeItem('persistent_device_info');
    return null;
  }
}

/**
 * Save device information to localStorage
 * @param {Object} deviceInfo - Device information to save
 */
export function saveDeviceInfo(deviceInfo) {
  try {
    const infoToSave = {
      ...deviceInfo,
      savedAt: new Date().toISOString()
    };

    // Save to regular storage
    localStorage.setItem('device_info', JSON.stringify(infoToSave));

    // If marked as persistent, also save to persistent storage
    if (deviceInfo.persistent) {
      localStorage.setItem('persistent_device_info', JSON.stringify(infoToSave));
      localStorage.setItem('persistent_device_id', deviceInfo.deviceId);
      console.log('💾 Device info saved to persistent storage:', infoToSave);
    }

    console.log('💾 Device info saved successfully:', infoToSave);
  } catch (error) {
    console.error('❌ Error saving device info:', error);
  }
}

/**
 * Clear saved device information
 */
export function clearSavedDeviceInfo() {
  try {
    localStorage.removeItem('device_info');
    localStorage.removeItem('persistent_device_info');
    localStorage.removeItem('persistent_device_id');
    console.log('🗑️ Saved device info cleared');
  } catch (error) {
    console.error('❌ Error clearing saved device info:', error);
  }
}

/**
 * Check if user session is valid (not expired)
 * @param {Object} loginInfo - Login information to check
 * @param {number} maxAgeHours - Maximum age in hours (default: 24)
 * @returns {boolean} True if session is valid
 */
export function isSessionValid(loginInfo, maxAgeHours = 24) {
  if (!loginInfo || !loginInfo.savedAt) {
    return false;
  }

  try {
    const savedTime = new Date(loginInfo.savedAt);
    const now = new Date();
    const ageInHours = (now - savedTime) / (1000 * 60 * 60);
    
    const isValid = ageInHours < maxAgeHours;
    console.log(`⏰ Session age: ${ageInHours.toFixed(2)} hours, valid: ${isValid}`);
    
    return isValid;
  } catch (error) {
    console.error('❌ Error checking session validity:', error);
    return false;
  }
}

/**
 * Get all saved app state for debugging
 * @returns {Object} All saved state
 */
export function getAllSavedState() {
  return {
    loginInfo: getSavedLoginInfo(),
    deviceInfo: getSavedDeviceInfo(),
    clientAppLogin: localStorage.getItem('client_app_login'),
    windowSize: localStorage.getItem('window_size')
  };
}
