/**
 * Token Authentication Manager
 * Handles device token-based authentication and session synchronization
 */

import React, { useEffect, useRef, useState } from 'react';
import { pbclient } from '../lib/pocketbase/pb';
import { saveLoginInfo, clearSavedLoginInfo } from '../utils/helper_functions';

const TokenAuthManager = ({ 
  deviceId, 
  onTokenLogin, 
  onTokenLogout, 
  onSessionUpdate,
  enabled = true 
}) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [currentToken, setCurrentToken] = useState(null);
  const [currentSession, setCurrentSession] = useState(null);
  const unsubscribeDeviceRef = useRef(null);
  const unsubscribeSessionRef = useRef(null);
  const checkIntervalRef = useRef(null);

  useEffect(() => {
    if (!deviceId || !enabled) {
      console.log('🔑 Token auth manager disabled or no device ID');
      return;
    }

    console.log('🔑 Setting up token authentication manager for device:', deviceId);
    setupTokenAuthentication();

    return () => {
      cleanup();
    };
  }, [deviceId, enabled]);

  const setupTokenAuthentication = async () => {
    try {
      setIsMonitoring(true);

      // Initial check for existing token
      await checkDeviceToken();

      // Subscribe to device changes for token updates
      unsubscribeDeviceRef.current = await pbclient.collection('devices').subscribe(deviceId, async function (e) {
        console.log('🔑 Device token update received:', e.action, e.record);

        if (e.action === 'update' && e.record) {
          await handleDeviceTokenUpdate(e.record);
        }
      });

      // Set up periodic token validation
      checkIntervalRef.current = setInterval(async () => {
        await checkDeviceToken();
      }, 30000); // Check every 30 seconds

      console.log('✅ Token authentication monitoring setup complete');
    } catch (error) {
      console.error('❌ Error setting up token authentication:', error);
      setIsMonitoring(false);
    }
  };

  const checkDeviceToken = async () => {
    try {
      console.log('🔍 Checking device token for:', deviceId);

      const device = await pbclient.collection('devices').getOne(deviceId);
      
      if (device.token && device.token !== currentToken) {
        console.log('🔑 New token detected, attempting authentication');
        await handleTokenLogin(device);
      } else if (!device.token && currentToken) {
        console.log('🚪 Token cleared, logging out');
        await handleTokenLogout();
      }

    } catch (error) {
      console.error('❌ Error checking device token:', error);
    }
  };

  const handleDeviceTokenUpdate = async (deviceRecord) => {
    try {
      if (deviceRecord.token && deviceRecord.token !== currentToken) {
        console.log('🔑 Token updated via real-time, attempting authentication');
        await handleTokenLogin(deviceRecord);
      } else if (!deviceRecord.token && currentToken) {
        console.log('🚪 Token cleared via real-time, logging out');
        await handleTokenLogout();
      }
    } catch (error) {
      console.error('❌ Error handling device token update:', error);
    }
  };

  const handleTokenLogin = async (device) => {
    try {
      console.log('🔑 Processing token login for device:', device.id);

      // Authenticate with PocketBase using the token
      pbclient.authStore.save(device.token, null);

      // Get the authenticated user
      const authData = await pbclient.collection('users').authRefresh();
      
      if (authData && authData.record) {
        const user = authData.record;
        console.log('✅ Token authentication successful:', user.username);

        // Update current token
        setCurrentToken(device.token);

        // Save login info
        const loginInfo = {
          isLoggedIn: true,
          username: user.username || user.email || 'Token User',
          userId: user.id,
          deviceId: deviceId,
          fromToken: true,
          token: device.token
        };
        saveLoginInfo(loginInfo);

        // Check for active session
        await checkActiveSession();

        // Notify parent component
        if (onTokenLogin) {
          onTokenLogin({
            user: user,
            token: device.token,
            device: device
          });
        }

        console.log('✅ Token login completed successfully');
      } else {
        throw new Error('Failed to get user data from token');
      }

    } catch (error) {
      console.error('❌ Error during token login:', error);
      
      // Clear invalid token
      setCurrentToken(null);
      pbclient.authStore.clear();
      
      if (onTokenLogout) {
        onTokenLogout();
      }
    }
  };

  const handleTokenLogout = async () => {
    try {
      console.log('🚪 Processing token logout');

      // Clear authentication
      pbclient.authStore.clear();
      setCurrentToken(null);
      setCurrentSession(null);

      // Clear saved login info
      clearSavedLoginInfo();

      // Clean up session subscription
      if (unsubscribeSessionRef.current) {
        unsubscribeSessionRef.current();
        unsubscribeSessionRef.current = null;
      }

      // Notify parent component
      if (onTokenLogout) {
        onTokenLogout();
      }

      console.log('✅ Token logout completed');
    } catch (error) {
      console.error('❌ Error during token logout:', error);
    }
  };

  const checkActiveSession = async () => {
    try {
      console.log('🎮 Checking for active session on device:', deviceId);

      const sessions = await pbclient.collection('sessions').getList(1, 1, {
        filter: `device = "${deviceId}" && (status = "Booked" || status = "Active" || status = "Occupied" || status = "Extended")`,
        sort: '-created'
      });

      if (sessions && sessions.items.length > 0) {
        const session = sessions.items[0];
        console.log('✅ Active session found:', session.id);
        
        setCurrentSession(session);

        // Subscribe to session updates
        if (unsubscribeSessionRef.current) {
          unsubscribeSessionRef.current();
        }

        unsubscribeSessionRef.current = await pbclient.collection('sessions').subscribe(session.id, function (e) {
          console.log('🎮 Session update received:', e.action, e.record);
          
          if (e.action === 'update' && e.record) {
            setCurrentSession(e.record);
            
            if (onSessionUpdate) {
              onSessionUpdate(e.record);
            }
          }
        });

        // Notify parent component
        if (onSessionUpdate) {
          onSessionUpdate(session);
        }

      } else {
        console.log('ℹ️ No active session found');
        setCurrentSession(null);
        
        if (onSessionUpdate) {
          onSessionUpdate(null);
        }
      }

    } catch (error) {
      console.error('❌ Error checking active session:', error);
    }
  };

  const cleanup = () => {
    console.log('🔄 Cleaning up token authentication manager');
    
    if (unsubscribeDeviceRef.current) {
      unsubscribeDeviceRef.current();
      unsubscribeDeviceRef.current = null;
    }
    
    if (unsubscribeSessionRef.current) {
      unsubscribeSessionRef.current();
      unsubscribeSessionRef.current = null;
    }
    
    if (checkIntervalRef.current) {
      clearInterval(checkIntervalRef.current);
      checkIntervalRef.current = null;
    }
    
    setIsMonitoring(false);
    setCurrentToken(null);
    setCurrentSession(null);
  };

  // This component is invisible - it only manages authentication
  return null;
};

export default TokenAuthManager;
