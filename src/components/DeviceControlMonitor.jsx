/**
 * Device Control Monitor Component
 * Monitors device boolean fields in PocketBase and executes Windows commands
 * when powerOff, reboot, lock, or sleep fields change to true
 */

import React, { useEffect, useRef, useState } from 'react';
import { pbclient } from '../lib/pocketbase/pb';
import { executeDeviceCommand } from '../utils/windowsCommands';

const DeviceControlMonitor = ({ deviceId, enabled = true }) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [lastCommandTime, setLastCommandTime] = useState(null);
  const unsubscribeRef = useRef(null);
  const commandCooldownRef = useRef(new Set());

  // Cooldown period to prevent rapid command execution (in milliseconds)
  const COMMAND_COOLDOWN = 10000; // 10 seconds

  useEffect(() => {
    if (!deviceId || !enabled) {
      console.log('🔧 Device control monitor disabled or no device ID');
      return;
    }

    console.log('🔧 Setting up device control monitor for device:', deviceId);
    setupDeviceControlMonitoring();

    return () => {
      cleanup();
    };
  }, [deviceId, enabled]);

  const setupDeviceControlMonitoring = async () => {
    try {
      setIsMonitoring(true);

      // Subscribe to device changes
      unsubscribeRef.current = await pbclient.collection('devices').subscribe(deviceId, async function (e) {
        console.log('🔧 Device control update received:', e.action, e.record);

        if (e.action === 'update' && e.record) {
          await handleDeviceControlUpdate(e.record);
        }
      });

      console.log('✅ Device control monitoring setup complete');
    } catch (error) {
      console.error('❌ Error setting up device control monitoring:', error);
      setIsMonitoring(false);
    }
  };

  const handleDeviceControlUpdate = async (deviceRecord) => {
    try {
      console.log('🔧 Checking device control fields:', {
        powerOff: deviceRecord.powerOff,
        reboot: deviceRecord.reboot,
        lock: deviceRecord.lock,
        sleep: deviceRecord.sleep
      });

      // Check each control field and execute command if true
      const commands = [
        { field: 'powerOff', command: 'poweroff', delay: 10 },
        { field: 'reboot', command: 'reboot', delay: 10 },
        { field: 'lock', command: 'lock', delay: 0 },
        { field: 'sleep', command: 'sleep', delay: 2 }
      ];

      for (const { field, command, delay } of commands) {
        if (deviceRecord[field] === true) {
          await executeControlCommand(field, command, delay, deviceRecord.id);
        }
      }

    } catch (error) {
      console.error('❌ Error handling device control update:', error);
    }
  };

  const executeControlCommand = async (field, command, delay, deviceId) => {
    try {
      // Check cooldown to prevent rapid execution
      const cooldownKey = `${field}_${deviceId}`;
      if (commandCooldownRef.current.has(cooldownKey)) {
        console.log(`⏰ Command ${field} is in cooldown, skipping execution`);
        return;
      }

      console.log(`🎮 Executing device control command: ${field} -> ${command}`);

      // Add to cooldown
      commandCooldownRef.current.add(cooldownKey);
      setTimeout(() => {
        commandCooldownRef.current.delete(cooldownKey);
      }, COMMAND_COOLDOWN);

      // Execute the Windows command
      const result = await executeDeviceCommand(command, {
        delay: delay,
        showNotification: true
      });

      // Update last command time
      setLastCommandTime(new Date());

      // Reset the boolean field in PocketBase to false
      await resetDeviceControlField(deviceId, field);

      // Log the command execution
      await logCommandExecution(deviceId, field, command, result);

      console.log(`✅ Device control command ${field} executed successfully:`, result);

    } catch (error) {
      console.error(`❌ Error executing device control command ${field}:`, error);

      // Still try to reset the field even if command failed
      try {
        await resetDeviceControlField(deviceId, field);
      } catch (resetError) {
        console.error('❌ Error resetting device control field:', resetError);
      }
    }
  };

  const resetDeviceControlField = async (deviceId, field) => {
    try {
      console.log(`🔄 Resetting device control field: ${field}`);

      const updateData = {};
      updateData[field] = false;

      await pbclient.collection('devices').update(deviceId, updateData);

      console.log(`✅ Device control field ${field} reset to false`);
    } catch (error) {
      console.error(`❌ Error resetting device control field ${field}:`, error);
      throw error;
    }
  };

  const logCommandExecution = async (deviceId, field, command, result) => {
    try {
      // Create a device log entry
      await pbclient.collection('device_logs').create({
        device: deviceId,
        status: result.success ? 'Available' : 'Maintainence',
        details: `Device control command executed: ${field} -> ${command}. Result: ${result.success ? 'Success' : 'Failed'}. ${result.output || result.error || ''}`
      });

      console.log(`📝 Device control command logged: ${field} -> ${command}`);
    } catch (error) {
      console.error('❌ Error logging device control command:', error);
    }
  };

  const cleanup = () => {
    if (unsubscribeRef.current) {
      console.log('🔄 Cleaning up device control monitoring');
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }
    setIsMonitoring(false);
    commandCooldownRef.current.clear();
  };

  // This component is invisible - it only monitors and executes commands
  return null;
};

export default DeviceControlMonitor;
