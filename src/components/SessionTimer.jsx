import { useEffect, useRef, useState } from 'react';
import { Button } from './ui/button';

function formatTime(ms) {
  const totalSeconds = Math.max(0, Math.floor(ms / 1000));
  const hours = Math.floor(totalSeconds / 3600).toString().padStart(2, '0');
  const minutes = Math.floor((totalSeconds % 3600) / 60).toString().padStart(2, '0');
  const seconds = (totalSeconds % 60).toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
}

const SessionTimer = ({ session, onEnd, onExtend, rate }) => {
  const [now, setNow] = useState(Date.now());
  const intervalRef = useRef();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    intervalRef.current = setInterval(() => setNow(Date.now()), 1000);
    return () => clearInterval(intervalRef.current);
  }, []);

  const startTime = new Date(session.in_time);
  const endTime = session.end_time ? new Date(session.end_time) : null;
  const elapsed = now - startTime.getTime();
  const timeLeft = endTime ? endTime.getTime() - now : null;

  // Auto-end for prepaid when time runs out
  useEffect(() => {
    if (endTime && timeLeft <= 0 && !loading) {
      console.log('Session time expired, auto-ending session');
      handleEndSession(true); // Pass true for auto-end
    }
    // eslint-disable-next-line
  }, [timeLeft, loading]);

  async function handleEndSession(autoEnd = false) {
    // Only ask for confirmation if not auto-ending
    if (!autoEnd && !window.confirm('Are you sure you want to end the session?')) return;

    setLoading(true);
    setError('');
    try {
      // Stop timer (handled by unmount)
      const now = new Date();
      const startTime = new Date(session.in_time);
      const timePlayed = Math.floor((now - startTime) / 60000); // minutes
      const sessionTotal = timePlayed * rate;
      // Get deviceId from localStorage
      let deviceId = session.device;
      try {
        const deviceInfo = localStorage.getItem('device_info');
        const parsedDeviceInfo = deviceInfo ? JSON.parse(deviceInfo) : null;
        if (parsedDeviceInfo?.deviceId) deviceId = parsedDeviceInfo.deviceId;
      } catch {}
      // Update session in PocketBase
      if (session && session.id) {
        await pbclient.collection('sessions').update(session.id, {
          out_time: now.toISOString(),
          status: 'Completed',
          time_played: timePlayed,
          session_total: sessionTotal,
          device: deviceId,
        });
        // Update device status
        await pbclient.collection('devices').update(deviceId, { status: 'Available' });
      }
      // Clear local session data
      localStorage.removeItem('session');
      localStorage.removeItem('device_info');
      // Redirect to login (call onEnd cleanup)
      if (onEnd) await onEnd(rate);
    } catch (err) {
      setError('Failed to end session. Please try again.');
      console.error('End session error:', err);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-lg w-full max-w-md">
        <h2 className="text-2xl font-bold mb-4 text-center">Session Active</h2>
        <div className="mb-4">
          <div className="text-lg">Time Used: <span className="font-mono">{formatTime(elapsed)}</span></div>
          {endTime && (
            <div className="text-lg">Time Left: <span className="font-mono">{formatTime(timeLeft)}</span></div>
          )}
          <div className="text-md mt-2">Payment Type: <b>{session.payment_type}</b></div>
        </div>
        {error && <div className="text-red-500 text-center mb-2">{error}</div>}
        <div className="flex gap-4 justify-center mt-6">
          {endTime && timeLeft <= 5 * 60 * 1000 && timeLeft > 0 && (
            <Button variant="outline" onClick={() => onExtend && onExtend(1)} disabled={loading}>
              Extend +1 hour
            </Button>
          )}
          <Button variant="destructive" onClick={handleEndSession} disabled={loading}>
            {loading ? (
              <span className="flex items-center"><svg className="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" /><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" /></svg>Ending...</span>
            ) : (
              'End Session'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SessionTimer;