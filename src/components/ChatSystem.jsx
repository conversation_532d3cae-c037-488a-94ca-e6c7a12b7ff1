import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Loader2, MessageCircle, Send, User, Headphones } from 'lucide-react';
import { pbclient } from '../lib/pocketbase/pb';
import { useAuth } from '../hooks/useAuth.jsx';

export default function ChatSystem({ deviceId }) {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState(null);
  const messagesEndRef = useRef(null);
  const { user } = useAuth();

  useEffect(() => {
    if (deviceId) {
      fetchMessages();
      subscribeToMessages();
    }
    
    return () => {
      // Cleanup subscription
      pbclient.collection('message').unsubscribe();
    };
  }, [deviceId]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const messagesList = await pbclient.collection('message').getFullList({
        filter: `device = "${deviceId}"`,
        sort: 'created',
        expand: 'sender'
      });
      setMessages(messagesList);
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const subscribeToMessages = async () => {
    try {
      // Subscribe to real-time message updates
      pbclient.collection('message').subscribe('*', function (e) {
        if (e.record.device === deviceId) {
          if (e.action === 'create') {
            setMessages(prev => [...prev, e.record]);
            
            // Mark server messages as read automatically
            if (!e.record.is_from_client) {
              markAsRead(e.record.id);
            }
          } else if (e.action === 'update') {
            setMessages(prev => prev.map(msg => 
              msg.id === e.record.id ? e.record : msg
            ));
          }
        }
      });
    } catch (error) {
      console.error('Error subscribing to messages:', error);
    }
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !user || sending) return;

    try {
      setSending(true);
      const messageData = {
        message: newMessage.trim(),
        device: deviceId,
        sender: user.id,
        is_from_client: true,
        is_read: false
      };

      await pbclient.collection('message').create(messageData);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message. Please try again.');
    } finally {
      setSending(false);
    }
  };

  const markAsRead = async (messageId) => {
    try {
      await pbclient.collection('message').update(messageId, { is_read: true });
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!deviceId) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
          <p className="text-muted-foreground">No device selected for chat</p>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">Loading chat...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="flex flex-col h-[500px]">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-blue-500" />
          Chat with Support
          <Badge variant="outline" className="ml-auto">
            Device: {deviceId.slice(-8)}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col p-0">
        {error && (
          <div className="p-4 bg-destructive/10 text-destructive text-sm">
            {error}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={fetchMessages}
              className="ml-2"
            >
              Retry
            </Button>
          </div>
        )}
        
        {/* Messages Container */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <MessageCircle className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No messages yet</p>
              <p className="text-sm">Start a conversation with support!</p>
            </div>
          ) : (
            messages.map(message => (
              <div 
                key={message.id} 
                className={`flex ${message.is_from_client ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-[70%] ${
                  message.is_from_client 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-muted'
                } rounded-lg p-3`}>
                  <div className="flex items-center gap-2 mb-1">
                    {message.is_from_client ? (
                      <User className="h-3 w-3" />
                    ) : (
                      <Headphones className="h-3 w-3" />
                    )}
                    <span className="text-xs font-medium">
                      {message.is_from_client ? 'You' : 'Support'}
                    </span>
                    <span className={`text-xs ${
                      message.is_from_client ? 'text-blue-100' : 'text-muted-foreground'
                    }`}>
                      {formatTime(message.created)}
                    </span>
                  </div>
                  <p className="text-sm">{message.message}</p>
                  {message.is_from_client && (
                    <div className="flex justify-end mt-1">
                      <span className={`text-xs ${
                        message.is_read ? 'text-blue-200' : 'text-blue-300'
                      }`}>
                        {message.is_read ? '✓✓' : '✓'}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className="border-t p-4">
          {user ? (
            <form onSubmit={sendMessage} className="flex gap-2">
              <Input
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type your message..."
                disabled={sending}
                className="flex-1"
              />
              <Button 
                type="submit" 
                disabled={!newMessage.trim() || sending}
                size="icon"
              >
                {sending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </form>
          ) : (
            <div className="text-center text-muted-foreground">
              <p className="text-sm">Please login to chat with support</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
