import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Loader2, Wallet, CreditCard, History, Gift } from 'lucide-react';
import { pbclient } from '../lib/pocketbase/pb';
import { useAuth } from '../hooks/useAuth.jsx';

export default function RechargeSystem() {
  const [rechargePlans, setRechargePlans] = useState([]);
  const [rechargeHistory, setRechargeHistory] = useState([]);
  const [walletBalance, setWalletBalance] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user, getCustomer } = useAuth();

  useEffect(() => {
    fetchRechargeData();
  }, [user]);

  const fetchRechargeData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch available recharge plans
      const plansData = await pbclient.collection('recharge_plans').getFullList({
        filter: 'status = "Active"',
        sort: 'price'
      });
      setRechargePlans(plansData);

      // Fetch user's wallet balance and recharge history if authenticated
      if (user) {
        const customer = await getCustomer();
        if (customer) {
          setWalletBalance(customer.wallet || 0);

          const history = await pbclient.collection('recharge_logs').getFullList({
            filter: `customer_id = "${customer.id}"`,
            expand: 'recharge_id',
            sort: '-created',
            limit: 10
          });
          setRechargeHistory(history);
        } else {
          // No customer record found - this client might not be linked to a customer yet
          console.log('No customer record found for this client');
          setWalletBalance(0);
          setRechargeHistory([]);
        }
      }
    } catch (error) {
      console.error('Error fetching recharge data:', error);
      setError('Failed to load recharge data');
    } finally {
      setLoading(false);
    }
  };

  const requestRecharge = async (planId) => {
    try {
      // For now, just show a message that admin needs to process it
      // In a real implementation, this would integrate with a payment gateway
      alert('Please contact admin to process this recharge. The admin will handle the payment and update your wallet balance.');
    } catch (error) {
      console.error('Error requesting recharge:', error);
      alert('Failed to request recharge. Please try again.');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateBonus = (price, value) => {
    const bonus = value - price;
    return bonus > 0 ? bonus : 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading recharge options...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-destructive">{error}</p>
        <Button onClick={fetchRechargeData} className="mt-2">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Wallet Balance */}
      {user && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wallet className="h-5 w-5 text-blue-600" />
              Wallet Balance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">
              ₹{walletBalance.toFixed(2)}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {walletBalance === 0 && rechargeHistory.length === 0
                ? 'No customer account linked. Contact admin to set up your account.'
                : 'Available for gaming sessions and purchases'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Recharge Plans */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <CreditCard className="h-5 w-5 text-green-500" />
          <h2 className="text-xl font-bold">Recharge Plans</h2>
        </div>
        
        {rechargePlans.length === 0 ? (
          <p className="text-muted-foreground">No recharge plans available at the moment.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {rechargePlans.map(plan => {
              const bonus = calculateBonus(plan.price, plan.value);
              return (
                <Card key={plan.id} className="relative">
                  {bonus > 0 && (
                    <div className="absolute -top-2 -right-2">
                      <Badge variant="destructive" className="bg-green-500">
                        <Gift className="h-3 w-3 mr-1" />
                        +₹{bonus}
                      </Badge>
                    </div>
                  )}
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      {plan.name}
                      <Badge variant="outline">Pay ₹{plan.price}</Badge>
                    </CardTitle>
                    <CardDescription>
                      Get ₹{plan.value} in your wallet
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="text-center p-3 bg-muted rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          ₹{plan.value}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Wallet Credit
                        </div>
                      </div>
                      
                      {plan.total_hours > 0 && (
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Gaming Hours:</span>
                          <span className="font-medium">{plan.total_hours} hours</span>
                        </div>
                      )}
                      
                      {plan.note && (
                        <p className="text-xs text-muted-foreground">
                          {plan.note}
                        </p>
                      )}
                      
                      <Button 
                        onClick={() => requestRecharge(plan.id)}
                        className="w-full"
                        disabled={!user}
                      >
                        {user ? 'Recharge Now' : 'Login Required'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Recharge History */}
      {user && (
        <div>
          <div className="flex items-center gap-2 mb-4">
            <History className="h-5 w-5 text-purple-500" />
            <h2 className="text-xl font-bold">Recharge History</h2>
          </div>
          
          {rechargeHistory.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground">No recharge history found.</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Your recharge transactions will appear here.
                </p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-0">
                <div className="divide-y">
                  {rechargeHistory.map(recharge => (
                    <div key={recharge.id} className="p-4 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <CreditCard className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {recharge.expand?.recharge_id?.name || 'Recharge'}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {formatDate(recharge.recharged_on)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600">
                          +₹{recharge.expand?.recharge_id?.value || 0}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Paid ₹{recharge.expand?.recharge_id?.price || 0}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
