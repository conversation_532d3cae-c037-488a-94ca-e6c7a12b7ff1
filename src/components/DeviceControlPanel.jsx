/**
 * Device Control Panel Component
 * Provides buttons to trigger device control commands by updating PocketBase boolean fields
 */

import React, { useState } from 'react';
import { pbclient } from '../lib/pocketbase/pb';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Power, RotateCcw, Lock, Moon, AlertTriangle, Zap } from 'lucide-react';

const DeviceControlPanel = ({ deviceId, currentDeviceInfo }) => {
  const [loading, setLoading] = useState(false);
  const [lastCommand, setLastCommand] = useState(null);
  const [error, setError] = useState('');

  const controlCommands = [
    {
      field: 'powerOff',
      label: 'Power Off',
      description: 'Shutdown the system',
      icon: Power,
      variant: 'destructive',
      dangerous: true,
      delay: 10
    },
    {
      field: 'reboot',
      label: 'Restart',
      description: 'Restart the system',
      icon: RotateCcw,
      variant: 'destructive',
      dangerous: true,
      delay: 10
    },
    {
      field: 'lock',
      label: 'Lock Screen',
      description: 'Lock the screen',
      icon: Lock,
      variant: 'secondary',
      dangerous: false,
      delay: 0
    },
    {
      field: 'sleep',
      label: 'Sleep',
      description: 'Put system to sleep',
      icon: Moon,
      variant: 'secondary',
      dangerous: false,
      delay: 2
    }
  ];

  const executeControlCommand = async (command) => {
    if (!deviceId) {
      setError('No device selected');
      return;
    }

    try {
      setLoading(true);
      setError('');

      console.log(`🎮 Triggering device control command: ${command.field}`);

      // Update the boolean field in PocketBase to trigger the command
      const updateData = {};
      updateData[command.field] = true;

      await pbclient.collection('devices').update(deviceId, updateData);

      setLastCommand({
        ...command,
        timestamp: new Date()
      });

      console.log(`✅ Device control command ${command.field} triggered successfully`);

    } catch (error) {
      console.error(`❌ Error triggering device control command ${command.field}:`, error);
      setError(`Failed to execute ${command.label}: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCommandClick = async (command) => {
    if (command.dangerous) {
      const confirmed = window.confirm(
        `⚠️ WARNING: This will ${command.description.toLowerCase()} in ${command.delay} seconds.\n\n` +
        `Are you sure you want to continue?\n\n` +
        `This action cannot be undone and will affect the current user session.`
      );

      if (!confirmed) {
        console.log(`❌ User cancelled dangerous command: ${command.field}`);
        return;
      }
    }

    await executeControlCommand(command);
  };

  if (!deviceId) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>No device selected. Please select a device to use device control.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Device Control Panel
        </CardTitle>
        <CardDescription>
          Control system functions for {currentDeviceInfo?.name || deviceId}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Device Info */}
        <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
          <div>
            <p className="font-medium">{currentDeviceInfo?.name || 'Unknown Device'}</p>
            <p className="text-sm text-muted-foreground">
              Type: {currentDeviceInfo?.type || 'Unknown'} • ID: {deviceId.slice(-8)}
            </p>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Last Command Display */}
        {lastCommand && (
          <Alert>
            <Zap className="h-4 w-4" />
            <AlertDescription>
              Last command: <strong>{lastCommand.label}</strong> triggered at{' '}
              {lastCommand.timestamp.toLocaleTimeString()}
            </AlertDescription>
          </Alert>
        )}

        {/* Control Buttons */}
        <div className="grid grid-cols-2 gap-3">
          {controlCommands.map((command) => {
            const IconComponent = command.icon;
            return (
              <Button
                key={command.field}
                variant={command.variant}
                onClick={() => handleCommandClick(command)}
                disabled={loading}
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <IconComponent className="h-6 w-6" />
                <div className="text-center">
                  <div className="font-medium">{command.label}</div>
                  <div className="text-xs opacity-75">{command.description}</div>
                  {command.dangerous && (
                    <Badge variant="destructive" className="mt-1 text-xs">
                      {command.delay}s delay
                    </Badge>
                  )}
                </div>
              </Button>
            );
          })}
        </div>

        {/* Warning */}
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Warning:</strong> These commands will affect the actual system. 
            Power Off and Restart commands have a {controlCommands.find(c => c.field === 'powerOff')?.delay}s delay.
            Use with caution!
          </AlertDescription>
        </Alert>

        {/* Instructions */}
        <div className="text-sm text-muted-foreground space-y-1">
          <p><strong>How it works:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>Buttons update boolean fields in PocketBase</li>
            <li>DeviceControlMonitor detects changes and executes Windows commands</li>
            <li>Fields are automatically reset to false after execution</li>
            <li>All actions are logged in device_logs collection</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default DeviceControlPanel;
