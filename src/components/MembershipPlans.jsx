import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Loader2, Crown, Clock, CheckCircle } from 'lucide-react';
import { pbclient } from '../lib/pocketbase/pb';
import { useAuth } from '../hooks/useAuth.jsx';

export default function MembershipPlans() {
  const [plans, setPlans] = useState([]);
  const [userMemberships, setUserMemberships] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user, getCustomer } = useAuth();

  useEffect(() => {
    fetchMembershipData();
  }, [user]);

  const fetchMembershipData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch available membership plans
      const plansData = await pbclient.collection('membership_plans').getFullList({
        filter: 'status = "Active"',
        sort: 'price'
      });
      setPlans(plansData);

      // Fetch user's active memberships if authenticated
      if (user) {
        const customer = await getCustomer();
        if (customer) {
          const memberships = await pbclient.collection('membership_logs').getFullList({
            filter: `customer_id = "${customer.id}" && expires_on > @now`,
            expand: 'plan_id',
            sort: '-created'
          });
          setUserMemberships(memberships);
        } else {
          // No customer record found - this client might not be linked to a customer yet
          console.log('No customer record found for this client');
          setUserMemberships([]);
        }
      }
    } catch (error) {
      console.error('Error fetching membership data:', error);
      setError('Failed to load membership data');
    } finally {
      setLoading(false);
    }
  };

  const requestMembership = async (planId) => {
    try {
      // For now, just show a message that admin needs to assign it
      // In a real implementation, this would integrate with a payment system
      alert('Please contact admin to purchase this membership plan. The admin will process your request and assign the membership.');
    } catch (error) {
      console.error('Error requesting membership:', error);
      alert('Failed to request membership. Please try again.');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const isExpired = (expiresOn) => {
    return new Date(expiresOn) <= new Date();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading memberships...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-destructive">{error}</p>
        <Button onClick={fetchMembershipData} className="mt-2">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Available Membership Plans */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Crown className="h-5 w-5 text-yellow-500" />
          <h2 className="text-xl font-bold">Available Membership Plans</h2>
        </div>
        
        {plans.length === 0 ? (
          <p className="text-muted-foreground">No membership plans available at the moment.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {plans.map(plan => (
              <Card key={plan.id} className="relative">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {plan.name}
                    <Badge variant="secondary">₹{plan.price}</Badge>
                  </CardTitle>
                  <CardDescription>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {plan.duration} days
                    </div>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    {plan.description}
                  </p>
                  
                  {plan.features && plan.features.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-medium mb-2">Features:</h4>
                      <ul className="space-y-1">
                        {plan.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-2 text-sm">
                            <CheckCircle className="h-3 w-3 text-green-500" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  <Button 
                    onClick={() => requestMembership(plan.id)}
                    className="w-full"
                    disabled={!user}
                  >
                    {user ? 'Request Membership' : 'Login Required'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* User's Active Memberships */}
      {user && (
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Badge className="h-5 w-5" />
            <h2 className="text-xl font-bold">My Active Memberships</h2>
          </div>
          
          {userMemberships.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground">
                  {user ? 'You don\'t have any active memberships.' : 'Please login to view your memberships.'}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  {user
                    ? 'Purchase a membership plan above to get started!'
                    : 'Contact admin if you need help setting up your account.'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {userMemberships.map(membership => (
                <Card key={membership.id} className="border-green-200">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      {membership.expand?.plan_id?.name || 'Unknown Plan'}
                      <Badge variant={isExpired(membership.expires_on) ? 'destructive' : 'default'}>
                        {isExpired(membership.expires_on) ? 'Expired' : 'Active'}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Expires:</span>
                        <span>{formatDate(membership.expires_on)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Started:</span>
                        <span>{formatDate(membership.created)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
