/**
 * Auto Session Creator Component
 * Automatically creates a 1-hour session when user logs in via client credentials
 */

import React, { useEffect, useRef, useState } from 'react';
import { pbclient } from '../lib/pocketbase/pb';

const AutoSessionCreator = ({ 
  deviceId, 
  userId, 
  username,
  isLoggedIn, 
  onSessionCreated, 
  onSessionError,
  enabled = true 
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [sessionCreated, setSessionCreated] = useState(false);
  const sessionCreatedRef = useRef(false);

  useEffect(() => {
    if (!enabled || !deviceId || !userId || !isLoggedIn || !username) {
      console.log('🎮 Auto session creator disabled or missing requirements', {
        enabled, deviceId, userId, isLoggedIn, username
      });
      return;
    }

    // Only create session once per login
    if (sessionCreatedRef.current) {
      console.log('🎮 Session already created for this login');
      return;
    }

    // Prevent multiple simultaneous creation attempts
    if (isCreating) {
      console.log('🎮 Session creation already in progress');
      return;
    }

    console.log('🎮 Auto session creator triggered for user:', username);

    // Add a small delay to ensure all state is settled and prevent race conditions
    const timeoutId = setTimeout(() => {
      if (!sessionCreatedRef.current && !isCreating) {
        createAutoSession();
      }
    }, 2000); // Increased delay to ensure app is fully loaded

    return () => clearTimeout(timeoutId);
  }, [deviceId, userId, isLoggedIn, username, enabled]);

  const createAutoSession = async () => {
    try {
      setIsCreating(true);
      console.log('🎮 Auto session creation started for user:', username, 'on device:', deviceId);

      // Check if there's already an active session on this device
      const existingSessions = await pbclient.collection('sessions').getList(1, 1, {
        filter: `device = "${deviceId}" && status = "Active"`,
        sort: '-created'
      });

      if (existingSessions && existingSessions.items.length > 0) {
        console.log('🎮 Active session already exists on device:', existingSessions.items[0].id);

        // Notify parent about existing session
        if (onSessionCreated) {
          onSessionCreated(existingSessions.items[0]);
        }

        sessionCreatedRef.current = true;
        setSessionCreated(true);
        return;
      }

      // Get device info to determine pricing
      const device = await pbclient.collection('devices').getOne(deviceId, {
        expand: 'group'
      });

      console.log('📱 Device info loaded:', device.name, 'Group:', device.expand?.group?.name);

      // Calculate session pricing based on device group
      const hourlyRate = device.expand?.group?.price || 60; // Default 60 per hour
      const sessionDuration = 60; // 1 hour in minutes
      const sessionTotal = (hourlyRate / 60) * sessionDuration; // Calculate proper cost

      console.log('💰 Session pricing calculated:', {
        hourlyRate: hourlyRate,
        duration: sessionDuration,
        sessionTotal: sessionTotal
      });

      // Create the session with proper fields
      const sessionData = {
        device: deviceId,
        in_time: new Date().toISOString(),
        duration: sessionDuration,
        session_total: sessionTotal,
        total_amount: sessionTotal,
        amount_paid: 0,
        status: 'Active',
        payment_mode: 'Post-paid',
        payment_type: 'Post-paid',
        time_played: 0,
        snacks_total: 0
      };

      console.log('📝 Creating session with data:', sessionData);

      const createdSession = await pbclient.collection('sessions').create(sessionData);

      console.log('✅ Auto session created successfully:', createdSession.id);

      // Update device status to Occupied
      try {
        await pbclient.collection('devices').update(deviceId, {
          status: 'Occupied'
        });
        console.log('✅ Device status updated to Occupied');
      } catch (deviceError) {
        console.error('❌ Error updating device status:', deviceError);
        // Don't fail the whole process for this
      }

      // Create session log entry
      try {
        await pbclient.collection('session_logs').create({
          session_id: createdSession.id,
          type: 'Create',
          session_amount: sessionTotal,
          details: `Auto session created for user: ${username} on device: ${device.name}. Duration: ${sessionDuration} minutes, Cost: $${sessionTotal.toFixed(2)}`
        });
        console.log('✅ Session log created');
      } catch (logError) {
        console.error('❌ Error creating session log:', logError);
        // Don't fail the whole process for this
      }

      // Create device log entry
      try {
        await pbclient.collection('device_logs').create({
          device: deviceId,
          status: 'Occupied',
          details: `Auto session started by ${username}. Duration: ${sessionDuration} minutes, Cost: $${sessionTotal.toFixed(2)}, Session ID: ${createdSession.id}`
        });
        console.log('✅ Device log created');
      } catch (deviceLogError) {
        console.error('❌ Error creating device log:', deviceLogError);
        // Don't fail the whole process for this
      }

      // Mark session as created
      sessionCreatedRef.current = true;
      setSessionCreated(true);

      // Notify parent component
      if (onSessionCreated) {
        onSessionCreated(createdSession);
      }

      console.log('✅ Auto session creation completed successfully');

    } catch (error) {
      console.error('❌ Error creating auto session:', error);

      // Don't crash the app - just log the error and continue
      try {
        if (onSessionError) {
          onSessionError(error);
        }
      } catch (callbackError) {
        console.error('❌ Error in session error callback:', callbackError);
      }

      // Reset the flag so user can try again later
      sessionCreatedRef.current = false;
      setSessionCreated(false);

      // Log the failure but don't break the app
      console.log('ℹ️ Auto session creation failed, but app continues normally');

    } finally {
      setIsCreating(false);
    }
  };

  // Reset session created flag when user logs out
  useEffect(() => {
    if (!isLoggedIn) {
      console.log('🔄 User logged out, resetting session creation flag');
      sessionCreatedRef.current = false;
      setSessionCreated(false);
    }
  }, [isLoggedIn]);

  // This component is invisible - it only creates sessions automatically
  return null;
};

export default AutoSessionCreator;
