import React, { useState, useEffect } from 'react';
import { pbclient } from '../lib/pocketbase/pb';
import { Monitor, Gamepad2, Smartphone, Headset, Loader2, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

const DeviceSelector = ({ onDeviceSelect }) => {
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDevice, setSelectedDevice] = useState(null);

  useEffect(() => {
    loadDevices();
  }, []);

  const loadDevices = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch only available devices from PocketBase
      const deviceList = await pbclient.collection('devices').getFullList({
        filter: 'status = "Available"',
        expand: 'group',
        sort: 'name'
      });

      console.log('📱 Loaded available devices:', deviceList.length);
      setDevices(deviceList);

    } catch (error) {
      console.error('Error loading devices:', error);
      setError('Failed to load devices: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeviceSelect = (device) => {
    console.log('🎯 Device selected in DeviceSelector:', device.id, device.name);
    setSelectedDevice(device.id);

    // Store device info and call callback
    if (onDeviceSelect) {
      console.log('📞 Calling onDeviceSelect callback');
      onDeviceSelect(device.id, device);
    } else {
      console.log('❌ No onDeviceSelect callback provided');
    }
  };

  const getDeviceIcon = (type) => {
    switch (type) {
      case 'PC': return <Monitor className="h-8 w-8" />;
      case 'PS': return <Gamepad2 className="h-8 w-8" />;
      case 'SIM': return <Smartphone className="h-8 w-8" />;
      case 'VR': return <Headset className="h-8 w-8" />;
      default: return <Monitor className="h-8 w-8" />;
    }
  };

  const getStatusVariant = (status) => {
    switch (status) {
      case 'Available': return 'default';
      case 'Occupied': return 'destructive';
      case 'Maintenance': return 'secondary';
      case 'Lost': return 'outline';
      case 'Damaged': return 'secondary';
      default: return 'outline';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Available': return 'text-green-600 bg-green-50 border-green-200';
      case 'Occupied': return 'text-red-600 bg-red-50 border-red-200';
      case 'Maintenance': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'Lost': return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'Damaged': return 'text-orange-600 bg-orange-50 border-orange-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="flex flex-col items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
          <p className="text-muted-foreground">Loading devices...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="text-center p-8">
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4 mb-4">
            <p className="text-destructive">{error}</p>
          </div>
          <Button onClick={loadDevices} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-3xl">Select Your Device</CardTitle>
        <CardDescription>Choose an available device to start your gaming session</CardDescription>
      </CardHeader>
      <CardContent className="p-6">

        {/* Available Devices Only */}
        {devices.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4 text-green-600">Available Devices ({devices.length})</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {devices.map((device) => (
                <Card
                  key={device.id}
                  onClick={() => handleDeviceSelect(device)}
                  className={`cursor-pointer transition-all hover:shadow-lg ${
                    selectedDevice === device.id
                      ? 'border-primary bg-primary/5'
                      : 'hover:border-primary/50'
                  }`}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="text-primary">
                        {getDeviceIcon(device.type)}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">{device.name}</h3>
                        <p className="text-sm text-muted-foreground">{device.type}</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <Badge variant={getStatusVariant(device.status)} className={getStatusColor(device.status)}>
                        {device.status}
                      </Badge>
                      {device.expand?.group && (
                        <span className="text-sm text-muted-foreground font-medium">
                          ₹{device.expand.group.price}/hr
                        </span>
                      )}
                    </div>

                    {device.ip_address && (
                      <p className="text-xs text-muted-foreground mt-2">IP: {device.ip_address}</p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* No devices found */}
        {devices.length === 0 && (
          <div className="text-center p-8">
            <p className="text-muted-foreground mb-4">No devices found in the database.</p>
            <Button onClick={loadDevices} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        )}

        {/* Refresh Button */}
        <div className="text-center mt-8">
          <Button
            variant="outline"
            onClick={loadDevices}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh List
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default DeviceSelector;
