import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Button } from './components/ui/button';
import { ModeToggle } from './components/theme/toggle-theme';
import { Label } from './components/ui/label';
import { computer, window as neuWindow, app, events } from '@neutralinojs/lib';
import Login from './components/Login';
import MembershipPlans from './components/MembershipPlans';
import RechargeSystem from './components/RechargeSystem';
import ChatSystem from './components/ChatSystem';
import SnacksInfo from './components/SnacksInfo';
import DeviceControlPanel from './components/DeviceControlPanel';
// import AutoLogin from './components/AutoLogin';
import DeviceRegistration from './components/DeviceRegistration';
import DeviceSelector from './components/DeviceSelector';
import DeviceControlMonitor from './components/DeviceControlMonitor';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './components/ui/tabs';
import { pbclient } from './lib/pocketbase/pb';
import { useCollection } from './hooks/useCollection';
import {
  getSavedLoginInfo,
  saveLoginInfo,
  clearSavedLoginInfo,
  getSavedDeviceInfo,
  saveDeviceInfo,
  isSessionValid,
  getAllSavedState
} from './utils/helper_functions';
import { Minus } from 'lucide-react';
import SessionTimer from './components/SessionTimer'; // [NEW] Session management UI

function App() {
  pbclient.autoCancellation(false);
  const [whichOs, setWhichOs] = useState('');
  const [isKioskMode, setIsKioskMode] = useState(false);
  const [currentTime, setCurrentTime] = useState('');
  const [currentDate, setCurrentDate] = useState('');
  const [testModeEnabled, setTestModeEnabled] = useState(false);
  const [isMinimizedToTray, setIsMinimizedToTray] = useState(false);

  // Device management with simplified selection
  const [deviceId, setDeviceId] = useState(() => {
    try {
      const deviceInfo = localStorage.getItem('device_info');
      const parsedDeviceInfo = deviceInfo ? JSON.parse(deviceInfo) : null;
      return parsedDeviceInfo?.deviceId || null;
    } catch (error) {
      console.error('Error parsing device info from localStorage:', error);
      return null;
    }
  });

  const [currentDeviceInfo, setCurrentDeviceInfo] = useState(null);
  const [showDeviceRegistration, setShowDeviceRegistration] = useState(false);
  const [showDeviceSelector, setShowDeviceSelector] = useState(false);

  // Authentication state
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [username, setUsername] = useState('');
  const [userId, setUserId] = useState(null);

  // App initialization state
  const [isAppInitialized, setIsAppInitialized] = useState(false);

  // [NEW] Session state
  const [session, setSession] = useState(() => {
    try {
      const saved = localStorage.getItem('session');
      return saved ? JSON.parse(saved) : null;
    } catch {
      return null;
    }
  });

  const { isAuthenticated, getAuthUser, logout } = useCollection('users');
  const { getList: getDevicesList } = useCollection('devices');

  const keyboardHandler = useRef(null);
  const autoLoginAttempted = useRef(false);

  // Handle device selection - store and continue with app
  const handleDeviceSelect = (deviceId, deviceData) => {
    console.log('🎯 Device selection started:', deviceId, deviceData);

    const deviceInfo = {
      deviceId: deviceId,
      deviceName: deviceData.name,
      deviceType: deviceData.type,
      selectedAt: new Date().toISOString()
    };

    try {
      // Save device info to localStorage
      localStorage.setItem('device_info', JSON.stringify(deviceInfo));

      // Update state immediately
      setDeviceId(deviceId);
      setCurrentDeviceInfo(deviceData);
      setShowDeviceSelector(false);
      setShowDeviceRegistration(false);

      console.log('✅ Device selected and stored');
      console.log('✅ Device selector closed');

    } catch (error) {
      console.error('❌ Error storing device info:', error);
    }
  };

  async function getOs() {
    try {
      const info = await computer.getOSInfo();
      console.log('OS Info:', info);
      setWhichOs(info.name + ' ' + info.version);
    } catch (error) {
      console.error('Error getting OS info:', error);
      setWhichOs('Error getting OS info');
    }
  }

  function updateTime() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    setCurrentTime(`${hours}:${minutes}:${seconds}`);

    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    setCurrentDate(now.toLocaleDateString(undefined, options));
  }

  async function enableKioskMode(reason = 'default') {
    if (testModeEnabled || isKioskMode) {
      console.log('Kiosk mode not enabled - test mode active or already enabled');
      return;
    }
    console.log(`🔓 Kiosk mode disabled to prevent system issues (would enable for: ${reason})`);
    setIsKioskMode(false);
    return;
  }

  async function toggleTestMode() {
    try {
      if (testModeEnabled) {
        await enableKioskMode();
        setTestModeEnabled(false);
        console.log('Test mode disabled - Kiosk mode enabled');
      } else {
        await disableKioskMode();
        setTestModeEnabled(true);
        console.log('Test mode enabled - Kiosk mode disabled');
      }
    } catch (error) {
      console.error('Error toggling test mode:', error);
    }
  }

  async function minimizeToTray() {
    try {
      console.log('Minimizing window to taskbar');
      await neuWindow.minimize();
      setIsMinimizedToTray(true);
      if (window.Neutralino && window.Neutralino.os && window.Neutralino.os.showNotification) {
        try {
          await window.Neutralino.os.showNotification({
            summary: 'Gaming Cafe Client',
            body: 'Application minimized to taskbar. Click the taskbar icon to restore.'
          });
        } catch (notifError) {
          console.log('Could not show notification:', notifError);
        }
      }
    } catch (error) {
      console.error('Error minimizing to tray:', error);
    }
  }

  async function restoreFromTray() {
    try {
      console.log('Restoring window from taskbar');
      await neuWindow.unminimize();
      await neuWindow.focus();
      setIsMinimizedToTray(false);
    } catch (error) {
      console.error('Error restoring from tray:', error);
    }
  }

  const validateDeviceId = useCallback(async (deviceIdToValidate) => {
    if (!deviceIdToValidate) return false;
    try {
      const device = await pbclient.collection('devices').getOne(deviceIdToValidate);
      return device;
    } catch (error) {
      console.error('Device validation failed:', error);
      return false;
    }
  }, []);

  const loadCurrentDeviceInfo = useCallback(async () => {
    const savedDeviceInfo = localStorage.getItem('device_info');
    if (savedDeviceInfo) {
      try {
        const parsedInfo = JSON.parse(savedDeviceInfo);
        if (parsedInfo.deviceId) {
          const device = await validateDeviceId(parsedInfo.deviceId);
          if (device) {
            setCurrentDeviceInfo(device);
            setDeviceId(device.id);
          } else {
            localStorage.removeItem('device_info');
            setCurrentDeviceInfo(null);
            setDeviceId(null);
          }
        }
      } catch (error) {
        console.error('Error loading current device info:', error);
        localStorage.removeItem('device_info');
        setCurrentDeviceInfo(null);
        setDeviceId(null);
      }
    }
  }, [validateDeviceId]);

  async function disableKioskMode(reason = 'default') {
    console.log(`🔓 Kiosk mode disabled to prevent system issues (would disable for: ${reason})`);
    setIsKioskMode(false);
    return;
  }

  // [NEW] Start session after login
  async function startSession(user, deviceId, paymentType) {
    const now = new Date();
    const sessionData = {
      in_time: now.toISOString(),
      status: 'Active',
      payment_type: paymentType,
      Membership: user.id,
      device: deviceId,
      session_total: 0,
      time_played: 0,
      ...(paymentType === 'Pre-paid' && {
        end_time: new Date(now.getTime() + 60 * 60 * 1000).toISOString()
      })
    };
    try {
      const created = await pbclient.collection('sessions').create(sessionData);
      setSession(created);
      localStorage.setItem('session', JSON.stringify(created));
      await pbclient.collection('devices').update(deviceId, { status: 'Active' });
    } catch (error) {
      console.error('Error starting session:', error);
    }
  }
  // [NEW] End session logic
  async function endSession(rate = 0) {
    if (!session) return;
    const now = new Date();
    const startTime = new Date(session.in_time);
    const timePlayed = Math.floor((now - startTime) / 60000); // minutes
    const sessionTotal = timePlayed * rate;
    try {
      await pbclient.collection('sessions').update(session.id, {
        out_time: now.toISOString(),
        status: 'Completed',
        time_played: timePlayed,
        session_total: sessionTotal,
        active: false
      });
      await pbclient.collection('devices').update(session.device, { status: 'Available' });
      localStorage.removeItem('session');
      setSession(null);
      setIsLoggedIn(false);
      setUsername('');
      setUserId(null);
      clearSavedLoginInfo();
      // NEW: Clear device info and show selector
      localStorage.removeItem('device_info');
      setDeviceId(null);
      setCurrentDeviceInfo(null);
      setShowDeviceSelector(true);
      setIsAppInitialized(false);
    } catch (error) {
      console.error('Error ending session:', error);
    }
  }
  // [NEW] Extend session logic (for prepaid)
  async function extendSession(hours = 1) {
    if (!session) return;
    try {
      const oldEnd = new Date(session.end_time);
      const newEnd = new Date(oldEnd.getTime() + hours * 60 * 60 * 1000);
      const updated = await pbclient.collection('sessions').update(session.id, {
        end_time: newEnd.toISOString()
      });
      setSession({ ...session, end_time: newEnd.toISOString() });
      localStorage.setItem('session', JSON.stringify({ ...session, end_time: newEnd.toISOString() }));
    } catch (error) {
      console.error('Error extending session:', error);
    }
  }

  // Handle device change (legacy function for DeviceRegistration component)
  const handleDeviceChange = (newDeviceId) => {
    console.log('Device changed to:', newDeviceId);
    setDeviceId(newDeviceId);
    setShowDeviceSelector(false);
    loadCurrentDeviceInfo();
    if (isLoggedIn) {
      console.log('User is logged in - device change may affect current session');
    }
  };

  useEffect(() => {
    if (!deviceId) return;
    console.log('🔄 Setting up real-time device monitoring for:', deviceId);
    let unsubscribe = null;
    const setupRealtimeMonitoring = async () => {
      try {
        unsubscribe = await pbclient.collection('devices').subscribe(deviceId, function (e) {
          console.log('📡 Device update received:', e.action, e.record);
          if (e.action === 'update' && e.record) {
            const device = e.record;
            if (device.token && !isLoggedIn) {
              console.log('🔑 Server app login detected via token, attempting auto-login');
              try {
                setIsLoggedIn(true);
                setUsername('Server User');
                setUserId('server-user-id');
                const serverLoginInfo = {
                  isLoggedIn: true,
                  username: 'Server User',
                  userId: 'server-user-id',
                  deviceId: deviceId,
                  fromServer: true
                };
                saveLoginInfo(serverLoginInfo);
                console.log('✅ Auto-login successful from server app (token-based)');
              } catch (error) {
                console.error('❌ Error during server auto-login:', error);
              }
            }
            if (!device.token && isLoggedIn) {
              console.log('🚪 Server app logout detected, logging out client');
              setIsLoggedIn(false);
              setUsername('');
              setUserId(null);
              clearSavedLoginInfo();
              console.log('✅ Auto-logout successful from server app');
            }
          }
        });
        console.log('✅ Real-time monitoring setup complete');
      } catch (error) {
        console.error('❌ Error setting up real-time monitoring:', error);
      }
    };
    setupRealtimeMonitoring();
    return () => {
      if (unsubscribe) {
        console.log('🔄 Cleaning up real-time monitoring');
        unsubscribe();
      }
    };
  }, [deviceId, isLoggedIn]);

  useEffect(() => {
    if (isAppInitialized) {
      console.log('🔄 App already initialized, skipping');
      return;
    }
    const initApp = async () => {
      console.log('🚀 App initialization started');
      try {
        await getOs();
        await loadCurrentDeviceInfo();
        const savedDeviceInfo = localStorage.getItem('device_info');
        console.log('📱 Saved device info:', savedDeviceInfo);
        let currentDeviceId = deviceId;
        if (!currentDeviceId && savedDeviceInfo) {
          try {
            const parsedDeviceInfo = JSON.parse(savedDeviceInfo);
            currentDeviceId = parsedDeviceInfo.deviceId;
            console.log('📱 Extracted deviceId from saved info:', currentDeviceId);
            setDeviceId(currentDeviceId);
            setCurrentDeviceInfo({
              name: parsedDeviceInfo.deviceName,
              type: parsedDeviceInfo.deviceType
            });
          } catch (parseError) {
            console.error('❌ Error parsing saved device info:', parseError);
          }
        }
        console.log('📱 Current deviceId state:', currentDeviceId);
        if (!savedDeviceInfo || !currentDeviceId) {
          console.log('⚠️ No device found, showing device selector');
          setShowDeviceSelector(true);
          setIsAppInitialized(true);
          return;
        }
        console.log('🔍 Validating device ID:', currentDeviceId);
        const validDevice = await validateDeviceId(currentDeviceId);
        if (!validDevice) {
          console.log('❌ Device validation failed, showing device selector');
          setShowDeviceSelector(true);
          setIsAppInitialized(true);
          return;
        }
        console.log('✅ Device validation successful, proceeding to app');
        setShowDeviceSelector(false);
        const savedLoginInfo = getSavedLoginInfo();
        console.log('👤 Saved login info:', savedLoginInfo);
        if (savedLoginInfo?.isLoggedIn) {
          if (isSessionValid(savedLoginInfo, 24)) {
            console.log("✅ Found valid saved login info, restoring user session");
            setIsLoggedIn(true);
            setUsername(savedLoginInfo.username);
            setUserId(savedLoginInfo.userId);
          } else {
            console.log("⏰ Saved login info expired, clearing and requiring new login");
            clearSavedLoginInfo();
          }
        } else {
          console.log("ℹ️ No saved login info found, user needs to login");
        }
        console.log("🔍 All saved state:", getAllSavedState());
        console.log('🔓 Kiosk mode initialization disabled to prevent system glitches');
        console.log('✅ App initialization completed');
        setIsAppInitialized(true);
      } catch (error) {
        console.error('❌ Error during app initialization:', error);
        setShowDeviceSelector(true);
        setIsAppInitialized(true);
      }
    };
    const handleKioskModeEvent = (event) => {
      console.log("🔓 Received enable-kiosk-mode event (disabled for safety):", event.detail);
      return;
    };
    window.addEventListener('enable-kiosk-mode', handleKioskModeEvent);
    const setupExitHandler = async () => {
      events.on('windowClose', () => {
        console.log('Window closing, exiting app');
        app.exit();
      });
    };
    updateTime();
    const timeInterval = setInterval(updateTime, 1000);
    initApp();
    setupExitHandler();
    return () => {
      clearInterval(timeInterval);
      window.removeEventListener('enable-kiosk-mode', handleKioskModeEvent);
      if (keyboardHandler.current) {
        document.removeEventListener('keydown', keyboardHandler.current, true);
      }
    };
  }, [testModeEnabled, loadCurrentDeviceInfo, validateDeviceId, enableKioskMode, disableKioskMode]);

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="flex justify-end p-2">
        <Button
          variant="ghost"
          size="icon"
          aria-label="Minimize"
          onClick={minimizeToTray}
          className="hover:bg-accent"
        >
          <Minus className="h-5 w-5" />
        </Button>
      </div>
      {session ? (
        <SessionTimer
          session={session}
          onEnd={endSession}
          onExtend={extendSession}
          rate={0} // TODO: Pass actual rate
        />
      ) : (
        <>
          {deviceId && <DeviceControlMonitor deviceId={deviceId} enabled={true} />}
          {showDeviceSelector && !showDeviceRegistration ? (
            <DeviceSelector onDeviceSelect={handleDeviceSelect} />
          ) : showDeviceRegistration ? (
            <div className="mt-16">
              <DeviceRegistration onDeviceRegistered={(newDeviceId) => {
                console.log("Device registered:", newDeviceId);
                setDeviceId(newDeviceId);
                setShowDeviceRegistration(false);
                try {
                  enableKioskMode();
                  console.log("Kiosk mode enabled after device registration (default state)");
                } catch (error) {
                  console.error("Error enabling kiosk mode after device registration:", error);
                }
              }} />
            </div>
          ) : !isLoggedIn ? (
            <>
              {isKioskMode && (
                <div className="fixed top-0 left-0 w-full bg-black text-white p-4 flex justify-between items-center">
                  <div className="text-xl font-bold">{currentTime}</div>
                  <div className="text-sm">{currentDate}</div>
                  <div className="text-sm">{whichOs}</div>
                </div>
              )}
              <div className="mt-16 space-y-6">
                <div className="text-center">
                  <p className="text-sm text-muted-foreground mb-4">
                    Please login to continue:
                  </p>
                  <Login onLogin={async (user) => {
                    setIsLoggedIn(true);
                    setUsername(user.username);
                    setUserId(user.id);
                    const loginDeviceId = deviceId || user.deviceId || null;
                    const loginInfo = {
                      isLoggedIn: true,
                      username: user.username,
                      userId: user.id,
                      deviceId: loginDeviceId
                    };
                    saveLoginInfo(loginInfo);
                    // [NEW] Determine payment type (replace with actual logic)
                    const paymentType = 'Pre-paid'; // or 'Post-paid'
                    await startSession(user, loginDeviceId, paymentType);
                  }} isKioskMode={isKioskMode} />
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="w-full max-w-6xl p-4">
                {(isLoggedIn && username) ? (
                  <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">
                      Welcome, {username}!
                    </h1>
                    <div className="flex items-center gap-4">
                      <Label>Current Time: {currentTime}</Label>
                      <ModeToggle />
                      {!isKioskMode && (
                        <Button
                          variant="outline"
                          onClick={minimizeToTray}
                          title="Minimize to taskbar"
                        >
                          Minimize
                        </Button>
                      )}
                      <Button
                        variant="destructive"
                        onClick={async () => {
                          try {
                            logout();
                            setIsLoggedIn(false);
                            setUsername('');
                            setUserId(null);
                            clearSavedLoginInfo();
                            console.log("🔓 Would enable kiosk mode after logout (disabled for safety)");
                          } catch (error) {
                            console.error("Error during logout:", error);
                            logout();
                            setIsLoggedIn(false);
                            setUsername('');
                            setUserId(null);
                            clearSavedLoginInfo();
                          }
                        }}
                      >
                        Logout
                      </Button>
                    </div>
                  </div>
                ) : null}
                <Tabs defaultValue="control" className="w-full">
                  <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="control">Control</TabsTrigger>
                    <TabsTrigger value="membership">Membership</TabsTrigger>
                    <TabsTrigger value="recharge">Recharge</TabsTrigger>
                    <TabsTrigger value="chat">Chat</TabsTrigger>
                    <TabsTrigger value="snacks">Snacks</TabsTrigger>
                  </TabsList>
                  <TabsContent value="control" className="mt-6">
                    <DeviceControlPanel
                      deviceId={deviceId}
                      currentDeviceInfo={currentDeviceInfo}
                    />
                  </TabsContent>
                  <TabsContent value="membership" className="mt-6">
                    <MembershipPlans />
                  </TabsContent>
                  <TabsContent value="recharge" className="mt-6">
                    <RechargeSystem />
                  </TabsContent>
                  <TabsContent value="chat" className="mt-6">
                    <ChatSystem deviceId={deviceId} />
                  </TabsContent>
                  <TabsContent value="snacks" className="mt-6">
                    <SnacksInfo />
                  </TabsContent>
                </Tabs>
                <div className="mt-6 p-4 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <Label htmlFor="osinfo">System: {whichOs}</Label>
                      <div className="mt-2">
                        <Label className={isKioskMode ? "text-destructive" : "text-green-500"}>
                          Kiosk Mode: {isKioskMode ? 'Enabled' : 'Disabled'}
                        </Label>
                      </div>
                      <div className="mt-2">
                        {currentDeviceInfo ? (
                          <>
                            <Label className="text-blue-600">
                              Device: {currentDeviceInfo.name} ({currentDeviceInfo.type})
                            </Label>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="ml-2 h-6 px-2 text-xs"
                              onClick={() => {
                                localStorage.removeItem('device_info');
                                setDeviceId(null);
                                setCurrentDeviceInfo(null);
                                setShowDeviceSelector(true);
                                setIsAppInitialized(false);
                                console.log('🔄 Device selection cleared, showing selector');
                              }}
                            >
                              Change
                            </Button>
                          </>
                        ) : (
                          <>
                            <Label className="text-orange-500">
                              ⚠️ No device selected
                            </Label>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="ml-2 h-6 px-2 text-xs"
                              onClick={() => setShowDeviceSelector(true)}
                            >
                              Select Device
                            </Button>
                          </>
                        )}
                      </div>
                      {testModeEnabled && (
                        <div className="mt-2">
                          <Label className="text-yellow-500 font-bold">
                            ⚠️ TEST MODE ACTIVE (Ctrl+Shift+T to toggle)
                          </Label>
                        </div>
                      )}
                      {isMinimizedToTray && (
                        <div className="mt-2">
                          <Label className="text-blue-500">
                            📱 Minimized to taskbar - Click taskbar icon to restore
                          </Label>
                        </div>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {currentDate}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </>
      )}
    </div>
  );
}

export default App;
