import { useState, useEffect } from 'react';
import { pbclient } from '../lib/pocketbase/pb';

export function useDeviceTokenListener(deviceId) {
  const [tokenData, setTokenData] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!deviceId) {
      console.log('No device ID provided to token listener');
      return;
    }

    let unsubscribe;

    const setupListener = async () => {
      try {
        console.log('Setting up device token listener for device:', deviceId);
        
        // Subscribe to device changes
        unsubscribe = await pbclient.collection('devices').subscribe(deviceId, function (e) {
          console.log('Device updated:', e.record);
          
          // Check if token was added/updated
          if (e.record.token && e.record.client_record) {
            try {
              const clientRecord = JSON.parse(e.record.client_record);
              const newTokenData = {
                token: e.record.token,
                client_id: e.record.client_id,
                client_username: e.record.client_username,
                client_name: e.record.client_name,
                client_email: e.record.client_email,
                login_time: e.record.login_time,
                clientRecord: clientRecord
              };
              
              console.log('Token detected, setting token data:', newTokenData);
              setTokenData(newTokenData);
              setError(null);
            } catch (parseError) {
              console.error('Error parsing client record:', parseError);
              setError('Failed to parse client data');
            }
          } else {
            // Token was cleared - client logged out
            console.log('Token cleared, removing token data');
            setTokenData(null);
            setError(null);
          }
        });

        setIsConnected(true);
        setError(null);

        // Check for existing token on mount
        try {
          const device = await pbclient.collection('devices').getOne(deviceId);
          console.log('Initial device check:', device);

          if (device.token && device.client_record) {
            try {
              const clientRecord = JSON.parse(device.client_record);
              const existingTokenData = {
                token: device.token,
                client_id: device.client_id,
                client_username: device.client_username,
                client_name: device.client_name,
                client_email: device.client_email,
                login_time: device.login_time,
                clientRecord: clientRecord
              };

              console.log('Existing token found:', existingTokenData);
              setTokenData(existingTokenData);
            } catch (parseError) {
              console.error('Error parsing existing client record:', parseError);
              setError('Failed to parse existing client data');
            }
          } else {
            console.log('No existing token found');
            setTokenData(null);
          }
        } catch (deviceError) {
          console.error('Error fetching device:', deviceError);
          if (deviceError.status === 404) {
            setError(`Device not found in database. Device ID: ${deviceId}`);
          } else {
            setError('Failed to fetch device information: ' + deviceError.message);
          }
        }

      } catch (error) {
        console.error('Error setting up device listener:', error);
        setIsConnected(false);
        setError('Failed to connect to device monitoring');
      }
    };

    setupListener();

    return () => {
      console.log('Cleaning up device token listener');
      if (unsubscribe) {
        unsubscribe();
      }
      setIsConnected(false);
    };
  }, [deviceId]);

  return { 
    tokenData, 
    isConnected, 
    error,
    hasToken: !!tokenData 
  };
}
