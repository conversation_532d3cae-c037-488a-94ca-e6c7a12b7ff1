import React, { useState, useEffect, createContext, useContext } from 'react';
import { pbclient } from '../lib/pocketbase/pb';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is already authenticated
    if (pbclient.authStore.isValid) {
      setUser(pbclient.authStore.model);
      setIsAuthenticated(true);
    }
    setLoading(false);

    // Listen for auth changes
    const unsubscribe = pbclient.authStore.onChange((token, model) => {
      setUser(model);
      setIsAuthenticated(!!model);
    });

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, []);

  const login = async (username, password) => {
    try {
      const authData = await pbclient.collection('clients').authWithPassword(username, password);
      setUser(authData.record);
      setIsAuthenticated(true);
      return authData;
    } catch (error) {
      throw error;
    }
  };

  const logout = () => {
    pbclient.authStore.clear();
    setUser(null);
    setIsAuthenticated(false);
  };

  const getCustomer = async () => {
    if (!user) return null;
    
    try {
      // First, find the xtreme_user by username
      const xtremeUser = await pbclient.collection('xtreme_users').getFirstListItem(
        `username = "${user.username}"`
      );
      
      // Then find the customer record associated with this xtreme_user
      const customer = await pbclient.collection('customers').getFirstListItem(
        `user = "${xtremeUser.id}"`
      );
      return customer;
    } catch (error) {
      console.error('Error fetching customer:', error);
      // If no customer found, this client might not have a customer record yet
      return null;
    }
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      isAuthenticated, 
      loading, 
      login, 
      logout, 
      getCustomer 
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
