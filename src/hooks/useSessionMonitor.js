import { useState, useEffect } from 'react';
import { pbclient } from '../lib/pocketbase/pb';

export function useSessionMonitor(deviceId) {
  const [currentSession, setCurrentSession] = useState(null);
  const [sessionLogs, setSessionLogs] = useState([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!deviceId) {
      console.log('No device ID provided to session monitor');
      return;
    }

    let sessionUnsubscribe;
    let logsUnsubscribe;
    let deviceUnsubscribe;

    const setupMonitoring = async () => {
      try {
        console.log('Setting up session monitoring for device:', deviceId);
        setIsMonitoring(true);
        setError(null);

        // Get initial device state
        const device = await pbclient.collection('devices').getOne(deviceId);
        console.log('Initial device state:', device);

        if (device.current_session) {
          await loadSession(device.current_session);
        }

        // Subscribe to device changes to detect new/ended sessions
        deviceUnsubscribe = await pbclient.collection('devices').subscribe(deviceId, async function (e) {
          console.log('Device updated for session monitoring:', e.record);
          
          const newSessionId = e.record.current_session;
          const currentSessionId = currentSession?.id;

          if (newSessionId !== currentSessionId) {
            if (newSessionId) {
              // New session started
              console.log('New session detected:', newSessionId);
              await loadSession(newSessionId);
            } else {
              // Session ended
              console.log('Session ended');
              setCurrentSession(null);
              setSessionLogs([]);
              
              // Clean up session subscription
              if (sessionUnsubscribe) {
                sessionUnsubscribe();
                sessionUnsubscribe = null;
              }
              if (logsUnsubscribe) {
                logsUnsubscribe();
                logsUnsubscribe = null;
              }
            }
          }
        });

      } catch (error) {
        console.error('Error setting up session monitoring:', error);
        setError('Failed to set up session monitoring: ' + error.message);
        setIsMonitoring(false);
      }
    };

    const loadSession = async (sessionId) => {
      try {
        console.log('Loading session:', sessionId);
        
        // Fetch the current session
        const session = await pbclient.collection('sessions').getOne(sessionId);
        console.log('Session loaded:', session);
        setCurrentSession(session);

        // Clean up previous session subscription
        if (sessionUnsubscribe) {
          sessionUnsubscribe();
        }

        // Subscribe to session updates
        sessionUnsubscribe = await pbclient.collection('sessions').subscribe(sessionId, function (e) {
          console.log('Session updated:', e.record);
          setCurrentSession(e.record);
        });

        // Fetch session logs
        const logs = await pbclient.collection('session_logs').getFullList({
          filter: `session_id = "${sessionId}"`,
          sort: '-created'
        });
        console.log('Session logs loaded:', logs);
        setSessionLogs(logs);

        // Clean up previous logs subscription
        if (logsUnsubscribe) {
          logsUnsubscribe();
        }

        // Subscribe to session logs
        logsUnsubscribe = await pbclient.collection('session_logs').subscribe('*', function (e) {
          if (e.record.session_id === sessionId) {
            console.log('Session log updated:', e.record);
            if (e.action === 'create') {
              setSessionLogs(prev => [e.record, ...prev]);
            } else if (e.action === 'update') {
              setSessionLogs(prev => prev.map(log => 
                log.id === e.record.id ? e.record : log
              ));
            }
          }
        });

      } catch (error) {
        console.error('Error loading session:', error);
        setError('Failed to load session: ' + error.message);
      }
    };

    setupMonitoring();

    return () => {
      console.log('Cleaning up session monitoring');
      if (sessionUnsubscribe) sessionUnsubscribe();
      if (logsUnsubscribe) logsUnsubscribe();
      if (deviceUnsubscribe) deviceUnsubscribe();
      setIsMonitoring(false);
    };
  }, [deviceId, currentSession?.id]);

  // Calculate session duration
  const getSessionDuration = () => {
    if (!currentSession?.in_time) return '00:00:00';
    
    const startTime = new Date(currentSession.in_time);
    const now = new Date();
    const diff = now - startTime;
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Get session status info
  const getSessionStatus = () => {
    if (!currentSession) return { status: 'No Session', color: 'gray' };
    
    const statusColors = {
      'Active': 'green',
      'Paused': 'yellow',
      'Completed': 'blue',
      'Cancelled': 'red'
    };
    
    return {
      status: currentSession.status || 'Unknown',
      color: statusColors[currentSession.status] || 'gray'
    };
  };

  return { 
    currentSession, 
    sessionLogs, 
    isMonitoring, 
    error,
    getSessionDuration,
    getSessionStatus,
    hasActiveSession: !!currentSession
  };
}
