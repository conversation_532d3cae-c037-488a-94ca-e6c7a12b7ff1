[{"id": "pbc_3646451221", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "clients", "type": "auth", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cost": 0, "hidden": true, "id": "password901924565", "max": 0, "min": 8, "name": "password", "pattern": "", "presentable": false, "required": true, "system": true, "type": "password"}, {"autogeneratePattern": "[a-zA-Z0-9]{50}", "hidden": true, "id": "text2504183744", "max": 60, "min": 30, "name": "<PERSON><PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"exceptDomains": null, "hidden": false, "id": "email3885137012", "name": "email", "onlyDomains": null, "presentable": false, "required": true, "system": true, "type": "email"}, {"hidden": false, "id": "bool1547992806", "name": "emailVisibility", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "bool256245529", "name": "verified", "presentable": false, "required": false, "system": true, "type": "bool"}, {"autogeneratePattern": "", "hidden": false, "id": "text4166911607", "max": 0, "min": 0, "name": "username", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file376926767", "maxSelect": 1, "maxSize": 0, "mimeTypes": [], "name": "avatar", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select1466534506", "maxSelect": 1, "name": "role", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Guest", "User"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_WKLjkQ7u4F` ON `clients` (`username`)", "CREATE UNIQUE INDEX `idx_tokenKey_pbc_3646451221` ON `clients` (`tokenKey`)", "CREATE UNIQUE INDEX `idx_email_pbc_3646451221` ON `clients` (`email`) WHERE `email` != ''"], "system": false, "authRule": "", "manageRule": null, "authAlert": {"enabled": true, "emailTemplate": {"subject": "Login from a new location", "body": "<p>Hello,</p>\n<p>We noticed a login to your {APP_NAME} account from a new location.</p>\n<p>If this was you, you may disregard this email.</p>\n<p><strong>If this wasn't you, you should immediately change your {APP_NAME} account password to revoke access from all other locations.</strong></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "oauth2": {"mappedFields": {"id": "", "name": "", "username": "", "avatarURL": ""}, "enabled": false}, "passwordAuth": {"enabled": true, "identityFields": ["username"]}, "mfa": {"enabled": false, "duration": 1800, "rule": ""}, "otp": {"enabled": false, "duration": 180, "length": 8, "emailTemplate": {"subject": "OTP for {APP_NAME}", "body": "<p>Hello,</p>\n<p>Your one-time password is: <strong>{OTP}</strong></p>\n<p><i>If you didn't ask for the one-time password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "authToken": {"duration": 604800}, "passwordResetToken": {"duration": 1800}, "emailChangeToken": {"duration": 1800}, "verificationToken": {"duration": 259200}, "fileToken": {"duration": 180}, "verificationTemplate": {"subject": "Verify your {APP_NAME} email", "body": "<p>Hello,</p>\n<p>Thank you for joining us at {APP_NAME}.</p>\n<p>Click on the button below to verify your email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-verification/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Verify</a>\n</p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "resetPasswordTemplate": {"subject": "Reset your {APP_NAME} password", "body": "<p>Hello,</p>\n<p>Click on the button below to reset your password.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-password-reset/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Reset password</a>\n</p>\n<p><i>If you didn't ask to reset your password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "confirmEmailChangeTemplate": {"subject": "Confirm your {APP_NAME} new email address", "body": "<p>Hello,</p>\n<p>Click on the button below to confirm your new email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-email-change/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Confirm new email</a>\n</p>\n<p><i>If you didn't ask to change your email address, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, {"id": "_pb_users_auth_", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "id = @request.auth.id", "name": "xtreme_users", "type": "auth", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cost": 0, "hidden": true, "id": "password901924565", "max": 0, "min": 8, "name": "password", "pattern": "", "presentable": false, "required": true, "system": true, "type": "password"}, {"autogeneratePattern": "[a-zA-Z0-9]{50}", "hidden": true, "id": "text2504183744", "max": 60, "min": 30, "name": "<PERSON><PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"exceptDomains": null, "hidden": false, "id": "email3885137012", "name": "email", "onlyDomains": null, "presentable": false, "required": true, "system": true, "type": "email"}, {"hidden": false, "id": "bool1547992806", "name": "emailVisibility", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "bool256245529", "name": "verified", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "file376926767", "maxSelect": 1, "maxSize": 0, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "name": "avatar", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"hidden": false, "id": "select1466534506", "maxSelect": 1, "name": "role", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Admin", "Staff"]}, {"autogeneratePattern": "", "hidden": false, "id": "text4166911607", "max": 0, "min": 0, "name": "username", "pattern": "^[a-z0-9_]+$", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_tokenKey__pb_users_auth_` ON `xtreme_users` (`tokenKey`)", "CREATE UNIQUE INDEX `idx_email__pb_users_auth_` ON `xtreme_users` (`email`) WHERE `email` != ''", "CREATE UNIQUE INDEX `idx_DRcHYUUQWh` ON `xtreme_users` (`username`)"], "system": false, "authRule": "", "manageRule": null, "authAlert": {"enabled": true, "emailTemplate": {"subject": "Login from a new location", "body": "<p>Hello,</p>\n<p>We noticed a login to your {APP_NAME} account from a new location.</p>\n<p>If this was you, you may disregard this email.</p>\n<p><strong>If this wasn't you, you should immediately change your {APP_NAME} account password to revoke access from all other locations.</strong></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "oauth2": {"mappedFields": {"id": "", "name": "", "username": "", "avatarURL": "avatar"}, "enabled": false}, "passwordAuth": {"enabled": true, "identityFields": ["email", "username"]}, "mfa": {"enabled": false, "duration": 1800, "rule": ""}, "otp": {"enabled": false, "duration": 180, "length": 8, "emailTemplate": {"subject": "OTP for {APP_NAME}", "body": "<p>Hello,</p>\n<p>Your one-time password is: <strong>{OTP}</strong></p>\n<p><i>If you didn't ask for the one-time password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "authToken": {"duration": 604800}, "passwordResetToken": {"duration": 1800}, "emailChangeToken": {"duration": 1800}, "verificationToken": {"duration": 259200}, "fileToken": {"duration": 180}, "verificationTemplate": {"subject": "Verify your {APP_NAME} email", "body": "<p>Hello,</p>\n<p>Thank you for joining us at {APP_NAME}.</p>\n<p>Click on the button below to verify your email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-verification/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Verify</a>\n</p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "resetPasswordTemplate": {"subject": "Reset your {APP_NAME} password", "body": "<p>Hello,</p>\n<p>Click on the button below to reset your password.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-password-reset/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Reset password</a>\n</p>\n<p><i>If you didn't ask to reset your password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "confirmEmailChangeTemplate": {"subject": "Confirm your {APP_NAME} new email address", "body": "<p>Hello,</p>\n<p>Click on the button below to confirm your new email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-email-change/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Confirm new email</a>\n</p>\n<p><i>If you didn't ask to change your email address, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, {"id": "pbc_2223581722", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "customers", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"hidden": false, "id": "number2087227935", "max": null, "min": null, "name": "wallet", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pre-paid", "Post-Paid"]}, {"autogeneratePattern": "", "hidden": false, "id": "text1281549880", "max": 0, "min": 0, "name": "contact", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select1466534506", "maxSelect": 1, "name": "membership", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Standard", "Member"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2303143954", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "device_logs", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2153001328", "hidden": false, "id": "relation154121870", "maxSelect": 1, "minSelect": 0, "name": "device", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Available", "Lost", "Damaged", "Maintainence"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1915095946", "max": 0, "min": 0, "name": "details", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2153001328", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "devices", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["PC", "PS", "SIM", "VR"]}, {"cascadeDelete": false, "collectionId": "pbc_3346940990", "hidden": false, "id": "relation1841317061", "maxSelect": 1, "minSelect": 0, "name": "group", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3072911721", "max": 0, "min": 0, "name": "mac_address", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text587191692", "max": 0, "min": 0, "name": "ip_address", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Available", "Occupied", "Maintainence", "Lost", "Damaged"]}, {"hidden": false, "id": "bool3741261079", "name": "powerOff", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "bool3243476108", "name": "reboot", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "bool2274335502", "name": "lock", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "bool255050412", "name": "sleep", "presentable": false, "required": false, "system": false, "type": "bool"}, {"autogeneratePattern": "", "hidden": false, "id": "text1597481275", "max": 0, "min": 0, "name": "token", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text434858273", "max": 0, "min": 0, "name": "client_id", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3602396850", "max": 0, "min": 0, "name": "client_username", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text2411707748", "max": 0, "min": 0, "name": "client_name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1157619907", "max": 0, "min": 0, "name": "client_email", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date2850427648", "max": "", "min": "", "name": "login_time", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "json3465729058", "maxSize": 0, "name": "client_record", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3346940990", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "groups", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "number3402113753", "max": null, "min": null, "name": "price", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["PC", "PS", "SIM", "VR"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3685203364", "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "name": "happy_hours", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3346940990", "hidden": false, "id": "relation1841317061", "maxSelect": 1, "minSelect": 0, "name": "group", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1345189255", "max": 0, "min": 0, "name": "start_time", "pattern": "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1096160257", "max": 0, "min": 0, "name": "end_time", "pattern": "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select3957652582", "maxSelect": 1, "name": "days", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Monday", "Tuesday", "Wednesday", "<PERSON>hr<PERSON><PERSON>", "Friday", "Saturday", "Sunday"]}, {"hidden": false, "id": "number1348932631", "max": null, "min": null, "name": "discount_percentage", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number3188078506", "max": null, "min": null, "name": "fixed_rate", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Active", "Inactive"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_656593255", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "login_logs", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date2852702992", "max": "", "min": "", "name": "login", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date902624559", "max": "", "min": "", "name": "logout", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3858505221", "listRule": "@collection.membership_logs.expires_on > @now", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "membership_logs", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2223581722", "hidden": false, "id": "relation2476065779", "maxSelect": 1, "minSelect": 0, "name": "customer_id", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_667222617", "hidden": false, "id": "relation3902341787", "maxSelect": 1, "minSelect": 0, "name": "plan_id", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date321019502", "max": "", "min": "", "name": "activated_on", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date1820088086", "max": "", "min": "", "name": "expires_on", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation627025380", "maxSelect": 1, "minSelect": 0, "name": "activated_by", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_667222617", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "membership_plans", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "number3402113753", "max": null, "min": null, "name": "price", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number2254405824", "max": null, "min": null, "name": "duration", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "json3217087507", "maxSize": 0, "name": "features", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Active", "Inactive"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_669929365", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "message", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3646451221", "hidden": false, "id": "relation1593854671", "maxSelect": 1, "minSelect": 0, "name": "sender", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2153001328", "hidden": false, "id": "relation154121870", "maxSelect": 1, "minSelect": 0, "name": "device", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3065852031", "max": 0, "min": 0, "name": "message", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool2601718512", "name": "is_from_client", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "bool228880198", "name": "is_read", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1607643979", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "recharge_logs", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2223581722", "hidden": false, "id": "relation2476065779", "maxSelect": 1, "minSelect": 0, "name": "customer_id", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_3055239267", "hidden": false, "id": "relation1227278416", "maxSelect": 1, "minSelect": 0, "name": "recharge_id", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date4145212327", "max": "", "min": "", "name": "recharged_on", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3245281325", "maxSelect": 1, "minSelect": 0, "name": "recharged_by", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3485334036", "max": 0, "min": 0, "name": "note", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "number2963754495", "max": null, "min": null, "name": "available_time", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3055239267", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "recharge_plans", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "number3402113753", "max": null, "min": null, "name": "price", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number494360628", "max": null, "min": null, "name": "value", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"autogeneratePattern": "", "hidden": false, "id": "text3485334036", "max": 0, "min": 0, "name": "note", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Active", "Inactive"]}, {"hidden": false, "id": "number1234170313", "max": null, "min": null, "name": "base_rate", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1231563711", "max": null, "min": null, "name": "total_hours", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_669024754", "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "name": "remote_commands", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2153001328", "hidden": false, "id": "relation2445630", "maxSelect": 1, "minSelect": 0, "name": "target_device", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2395663060", "maxSelect": 1, "name": "command", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Shutdown", "Reboot", "Sleep", "Lock", "Unlock"]}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["pending", "executed", "failed"]}, {"autogeneratePattern": "", "hidden": false, "id": "text3437106334", "max": 0, "min": 0, "name": "output", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3725765462", "maxSelect": 1, "minSelect": 0, "name": "created_by", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date898477131", "max": "", "min": "", "name": "executed_by", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3933996135", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "screenshots", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2153001328", "hidden": false, "id": "relation154121870", "maxSelect": 1, "minSelect": 0, "name": "device", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "file3309110367", "maxSelect": 1, "maxSize": 0, "mimeTypes": [], "name": "image", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1634108016", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "session_logs", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3660498186", "hidden": false, "id": "relation1631579359", "maxSelect": 1, "minSelect": 0, "name": "session_id", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Create", "Extend", "Closed"]}, {"hidden": false, "id": "number3788143131", "max": null, "min": null, "name": "session_amount", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2491211509", "maxSelect": 1, "minSelect": 0, "name": "billed_by", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_91659701", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "session_snacks_logs", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3660498186", "hidden": false, "id": "relation3494172116", "maxSelect": 1, "minSelect": 0, "name": "session", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2875923803", "hidden": false, "id": "relation2114768017", "maxSelect": 1, "minSelect": 0, "name": "snack", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "number2683508278", "max": null, "min": null, "name": "quantity", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number3402113753", "max": null, "min": null, "name": "price", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number2287204505", "max": null, "min": null, "name": "each_price", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3660498186", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "sessions", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"hidden": false, "id": "date1160750881", "max": "", "min": "", "name": "in_time", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date3063530086", "max": "", "min": "", "name": "out_time", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "number3636207680", "max": null, "min": null, "name": "snacks_total", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number424827272", "max": null, "min": null, "name": "session_total", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1186288468", "max": null, "min": null, "name": "total_amount", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number2484990468", "max": null, "min": null, "name": "amount_paid", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number3772865661", "max": null, "min": null, "name": "discount_amount", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1097742979", "max": null, "min": null, "name": "discount_rate", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number2254405824", "max": null, "min": null, "name": "duration", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Active", "Closed", "Booked", "Extended"]}, {"hidden": false, "id": "select3058290911", "maxSelect": 1, "name": "payment_mode", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Cash", "UPI", "Membership", "Pre-paid", "Post-paid", "Part-paid"]}, {"hidden": false, "id": "select2908602461", "maxSelect": 1, "name": "payment_type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pre-paid", "Post-paid"]}, {"hidden": false, "id": "number2794146439", "max": null, "min": null, "name": "Cash", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number546148649", "max": null, "min": null, "name": "UPI", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number3382890837", "max": null, "min": null, "name": "Membership", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"cascadeDelete": false, "collectionId": "pbc_2153001328", "hidden": false, "id": "relation154121870", "maxSelect": 1, "minSelect": 0, "name": "device", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "number893092034", "max": null, "min": null, "name": "time_played", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number3956427367", "max": null, "min": null, "name": "adjusted_deduction", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2875923803", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "snacks", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Eatable", "Drinkable"]}, {"hidden": false, "id": "number2683508278", "max": null, "min": null, "name": "quantity", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select1587448267", "maxSelect": 1, "name": "location", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Stock", "<PERSON><PERSON>"]}, {"hidden": false, "id": "number2737583402", "max": null, "min": null, "name": "selling_price", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Available", "Unavailable", "Low Stock"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1231783484", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "snacks_logs", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2875923803", "hidden": false, "id": "relation4100572128", "maxSelect": 1, "minSelect": 0, "name": "snack_id", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "number2683508278", "max": null, "min": null, "name": "quantity", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select1587448267", "maxSelect": 1, "name": "location", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Stock", "<PERSON><PERSON>"]}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Lost", "Purchase", "Expired", "Used", "Price Changed", "Location changed"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}]