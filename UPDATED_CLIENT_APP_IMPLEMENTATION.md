# Updated Client App Implementation Guide

## 🎯 **Updated Flow with Your Schema**

### **Your Schema Structure:**
- ✅ `devices` - stores client login tokens and info
- ✅ `clients` - auth collection for client authentication  
- ✅ `sessions` - independent session data (no client info)
- ✅ `session_logs` - tracks session creation/billing

### **Perfect Data Flow:**
1. **Server**: Client login → Token stored in `devices` collection
2. **Client**: Detects token → Auto-login using `clients` auth
3. **Server**: Creates session in `sessions` + log in `session_logs`
4. **Client**: Monitors session status via device token
5. **Server**: Ends session → Clears token from device
6. **Client**: Detects token removal → Ends session

## 📱 **Client App Implementation**

### **1. Device Token Listener (Core Component)**

```javascript
// hooks/useDeviceTokenListener.js
import { useState, useEffect } from 'react';
import pb from '../lib/pocketbase';

export function useDeviceTokenListener(deviceId) {
  const [tokenData, setTokenData] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!deviceId) return;

    let unsubscribe;

    const setupListener = async () => {
      try {
        // Subscribe to device changes
        unsubscribe = await pb.collection('devices').subscribe(deviceId, function (e) {
          console.log('Device updated:', e.record);
          
          // Check if token was added/updated
          if (e.record.token && e.record.client_record) {
            try {
              const clientRecord = JSON.parse(e.record.client_record);
              setTokenData({
                token: e.record.token,
                client_id: e.record.client_id,
                client_username: e.record.client_username,
                client_name: e.record.client_name,
                client_email: e.record.client_email,
                login_time: e.record.login_time,
                clientRecord: clientRecord
              });
            } catch (error) {
              console.error('Error parsing client record:', error);
            }
          } else {
            // Token was cleared - client logged out
            setTokenData(null);
          }
        });

        setIsConnected(true);

        // Check for existing token on mount
        const device = await pb.collection('devices').getOne(deviceId);
        if (device.token && device.client_record) {
          try {
            const clientRecord = JSON.parse(device.client_record);
            setTokenData({
              token: device.token,
              client_id: device.client_id,
              client_username: device.client_username,
              client_name: device.client_name,
              client_email: device.client_email,
              login_time: device.login_time,
              clientRecord: clientRecord
            });
          } catch (error) {
            console.error('Error parsing existing client record:', error);
          }
        }

      } catch (error) {
        console.error('Error setting up device listener:', error);
        setIsConnected(false);
      }
    };

    setupListener();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [deviceId]);

  return { tokenData, isConnected };
}
```

### **2. Auto-Login Component**

```javascript
// components/AutoLogin.jsx
import { useEffect, useState } from 'react';
import { useDeviceTokenListener } from '../hooks/useDeviceTokenListener';
import pb from '../lib/pocketbase';

export default function AutoLogin({ deviceId, onLoginSuccess, onLogout }) {
  const { tokenData, isConnected } = useDeviceTokenListener(deviceId);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  useEffect(() => {
    if (tokenData) {
      console.log('Token detected, attempting auto-login:', tokenData);
      autoLoginClient(tokenData);
    } else if (isAuthenticated) {
      console.log('Token removed, logging out client');
      handleLogout();
    }
  }, [tokenData]);

  const autoLoginClient = async (tokenData) => {
    try {
      // Set the auth store with the token and client record
      pb.authStore.save(tokenData.token, tokenData.clientRecord);
      
      // Verify the token is still valid by making a test request
      await pb.collection('clients').authRefresh();
      
      console.log('Auto-login successful!');
      setIsAuthenticated(true);
      setCurrentUser(tokenData.clientRecord);
      
      // Notify parent component
      if (onLoginSuccess) {
        onLoginSuccess(tokenData.clientRecord, tokenData);
      }
      
    } catch (error) {
      console.error('Auto-login failed:', error);
      // Token might be expired, clear it
      pb.authStore.clear();
      setIsAuthenticated(false);
      setCurrentUser(null);
    }
  };

  const handleLogout = () => {
    pb.authStore.clear();
    setIsAuthenticated(false);
    setCurrentUser(null);
    
    if (onLogout) {
      onLogout();
    }
  };

  return (
    <div className="auto-login-status">
      <div className="flex items-center gap-2 text-sm">
        <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
        <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
      </div>
      
      {tokenData && (
        <div className="mt-2 text-xs text-gray-600">
          <div>Client: {tokenData.client_name}</div>
          <div>Login: {new Date(tokenData.login_time).toLocaleString()}</div>
        </div>
      )}
    </div>
  );
}
```

### **3. Session Monitor**

```javascript
// hooks/useSessionMonitor.js
import { useState, useEffect } from 'react';
import pb from '../lib/pocketbase';

export function useSessionMonitor(deviceId) {
  const [currentSession, setCurrentSession] = useState(null);
  const [sessionLogs, setSessionLogs] = useState([]);

  useEffect(() => {
    if (!deviceId) return;

    let sessionUnsubscribe;
    let logsUnsubscribe;

    const setupMonitoring = async () => {
      try {
        // Monitor device for current_session changes
        const device = await pb.collection('devices').getOne(deviceId);
        if (device.current_session) {
          // Fetch the current session
          const session = await pb.collection('sessions').getOne(device.current_session);
          setCurrentSession(session);

          // Subscribe to session updates
          sessionUnsubscribe = await pb.collection('sessions').subscribe(device.current_session, function (e) {
            console.log('Session updated:', e.record);
            setCurrentSession(e.record);
          });

          // Fetch session logs
          const logs = await pb.collection('session_logs').getFullList({
            filter: `session_id = "${device.current_session}"`,
            sort: '-created'
          });
          setSessionLogs(logs);

          // Subscribe to session logs
          logsUnsubscribe = await pb.collection('session_logs').subscribe('*', function (e) {
            if (e.record.session_id === device.current_session) {
              if (e.action === 'create') {
                setSessionLogs(prev => [e.record, ...prev]);
              }
            }
          });
        }

        // Subscribe to device changes to detect new sessions
        await pb.collection('devices').subscribe(deviceId, async function (e) {
          if (e.record.current_session !== device.current_session) {
            if (e.record.current_session) {
              // New session started
              const newSession = await pb.collection('sessions').getOne(e.record.current_session);
              setCurrentSession(newSession);
            } else {
              // Session ended
              setCurrentSession(null);
              setSessionLogs([]);
            }
          }
        });

      } catch (error) {
        console.error('Error setting up session monitoring:', error);
      }
    };

    setupMonitoring();

    return () => {
      if (sessionUnsubscribe) sessionUnsubscribe();
      if (logsUnsubscribe) logsUnsubscribe();
    };
  }, [deviceId]);

  return { currentSession, sessionLogs };
}
```

### **4. Main Client App Component**

```javascript
// App.jsx
import { useState, useEffect } from 'react';
import AutoLogin from './components/AutoLogin';
import { useSessionMonitor } from './hooks/useSessionMonitor';
import pb from './lib/pocketbase';

export default function App() {
  const deviceId = 'your-device-id'; // Get this from your device identification
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [sessionStartTime, setSessionStartTime] = useState(null);
  
  const { currentSession, sessionLogs } = useSessionMonitor(deviceId);

  const handleLoginSuccess = (user, tokenData) => {
    setIsLoggedIn(true);
    setCurrentUser(user);
    setSessionStartTime(new Date());
    console.log('Client logged in:', user.name);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setCurrentUser(null);
    setSessionStartTime(null);
    console.log('Client logged out');
  };

  // Calculate session duration
  const getSessionDuration = () => {
    if (!sessionStartTime) return '00:00:00';
    
    const now = new Date();
    const diff = now - sessionStartTime;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="client-app">
      {/* Header */}
      <div className="header bg-blue-600 text-white p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold">Gaming Station</h1>
          <AutoLogin 
            deviceId={deviceId}
            onLoginSuccess={handleLoginSuccess}
            onLogout={handleLogout}
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="main-content p-6">
        {isLoggedIn ? (
          <div className="space-y-6">
            {/* User Info */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-green-800 mb-2">Welcome, {currentUser?.name}!</h2>
              <div className="grid grid-cols-2 gap-4 text-sm text-green-700">
                <div>Username: {currentUser?.username}</div>
                <div>Email: {currentUser?.email}</div>
                <div>Session Time: {getSessionDuration()}</div>
                <div>Status: {currentSession?.status || 'No Session'}</div>
              </div>
            </div>

            {/* Session Info */}
            {currentSession && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">Current Session</h3>
                <div className="grid grid-cols-2 gap-4 text-sm text-blue-700">
                  <div>Session ID: {currentSession.id}</div>
                  <div>Status: {currentSession.status}</div>
                  <div>Start Time: {new Date(currentSession.in_time).toLocaleString()}</div>
                  <div>Duration: {currentSession.duration} minutes</div>
                  <div>Payment Type: {currentSession.payment_type}</div>
                  <div>Total Amount: ₹{currentSession.total_amount}</div>
                </div>
              </div>
            )}

            {/* Session Logs */}
            {sessionLogs.length > 0 && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Session Activity</h3>
                <div className="space-y-2">
                  {sessionLogs.map(log => (
                    <div key={log.id} className="flex justify-between items-center text-sm">
                      <span className="font-medium">{log.type}</span>
                      <span>₹{log.session_amount}</span>
                      <span className="text-gray-500">{new Date(log.created).toLocaleString()}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <h2 className="text-2xl font-semibold text-gray-600 mb-4">Waiting for Login</h2>
            <p className="text-gray-500">Please login from the server to start your session</p>
          </div>
        )}
      </div>
    </div>
  );
}
```

## 🔧 **Environment Setup**

```env
# .env file in client app
VITE_POCKETBASE_URL=http://your-server-url:8090
VITE_DEVICE_ID=your-device-id
```

## 🎯 **Key Features Implemented**

1. **Real-time Token Detection** - Instantly detects when server logs in client
2. **Auto-Login** - Automatically authenticates client using stored token
3. **Session Monitoring** - Tracks current session and logs in real-time
4. **Auto-Logout** - Automatically logs out when server clears token
5. **Session Duration** - Shows live session timer
6. **Activity Logs** - Displays session creation/billing events

This implementation works perfectly with your existing schema and provides a seamless client experience!
