{"name": "vite-project", "version": "1.0.0-alpha", "type": "module", "scripts": {"init": "neu update", "start": "neu run -- --window-enable-inspector=true", "build": "neu build", "vite-dev": "vite", "vite-build": "vite build"}, "dependencies": {"@neutralinojs/lib": "^5.1.0", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@tailwindcss/vite": "^4.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "pocketbase": "^0.26.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7"}, "devDependencies": {"@neutralinojs/neu": "^11.0.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.23.0", "eslint-plugin-react": "^7.31.4", "tw-animate-css": "^1.3.0", "vite": "^5.2.0"}, "engines": {"node": ">=20", "npm": ">=10"}}